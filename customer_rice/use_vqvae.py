﻿import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser, Select
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
import matplotlib.pyplot as plt
from tqdm import tqdm

# 忽略PDB解析中的一些警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)

# 1. PDB文件解析器
class ProteinDataset(Dataset):
    """从PDB文件加载蛋白质原子数据的Dataset类"""
    
    # 常见的蛋白质原子类型映射
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7, 
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14, 
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21, 
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28, 
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35, 
        # 添加其他原子类型...
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }
    
    def __init__(self, pdb_dir, max_atoms=1000):
        """
        pdb_dir: 包含PDB文件的目录
        max_atoms: 每个蛋白质的最大原子数（填充或截断）
        """
        self.pdb_files = [os.path.join(pdb_dir, f) for f in os.listdir(pdb_dir) 
                          if f.endswith('.pdb')]
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        
        # 收集所有原子的统计数据
        self.atom_coords = []
        self.atom_types = []
        
        print(f"Found {len(self.pdb_files)} PDB files. Processing...")
        
    def __len__(self):
        return len(self.pdb_files)
    
    def __getitem__(self, idx):
        pdb_file = self.pdb_files[idx]
        
        try:
            # 解析PDB文件
            structure = self.parser.get_structure("protein", pdb_file)
            
            # 提取所有原子
            atoms = []
            for model in structure:
                for chain in model:
                    for residue in chain:
                        for atom in residue:
                            atoms.append(atom)
            
            # 提取坐标和类型
            coords = []
            types = []
            for atom in atoms:
                # 获取原子类型，如果不在映射中则使用默认值
                atom_name = atom.get_name().strip()
                atom_type_idx = self.ATOM_TYPE_MAP.get(atom_name, len(self.ATOM_TYPE_MAP))
                coords.append(atom.get_coord())
                types.append(atom_type_idx)
            
            # 转换为numpy数组
            coords = np.array(coords, dtype=np.float32)
            types = np.array(types, dtype=np.int64)
            
            # 随机选择最大原子数（如果原子数超过限制）
            if len(coords) > self.max_atoms:
                indices = np.random.choice(len(coords), self.max_atoms, replace=False)
                coords = coords[indices]
                types = types[indices]
            
            # 填充到最大原子数
            num_atoms = len(coords)
            if num_atoms < self.max_atoms:
                pad_size = self.max_atoms - num_atoms
                coords = np.pad(coords, ((0, pad_size), (0, 0)), 
                                mode='constant', constant_values=0)
                types = np.pad(types, (0, pad_size), mode='constant', 
                              constant_values=len(self.ATOM_TYPE_MAP))
            
            # 坐标归一化（每个蛋白质单独归一化）
            coords_mean = coords.mean(axis=0)
            coords_std = coords.std(axis=0) + 1e-8  # 避免除以零
            coords = (coords - coords_mean) / coords_std
            
            return {
                'coords': torch.tensor(coords, dtype=torch.float32),
                'types': torch.tensor(types, dtype=torch.long),
                'mask': torch.tensor([1] * num_atoms + [0] * (self.max_atoms - num_atoms), 
                                    dtype=torch.bool)
            }
        
        except Exception as e:
            print(f"Error processing {pdb_file}: {str(e)}")
            # 返回空数据
            return {
                'coords': torch.zeros((self.max_atoms, 3), dtype=torch.float32),
                'types': torch.zeros(self.max_atoms, dtype=torch.long),
                'mask': torch.zeros(self.max_atoms, dtype=torch.bool)
            }

# 2. VQ-VAE模型定义
class VectorQuantizer(nn.Module):
    """向量量化层"""
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.embedding_dim = embedding_dim
        self.num_embeddings = num_embeddings
        self.commitment_cost = commitment_cost
        
        # 初始化码本
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1/num_embeddings, 1/num_embeddings)

    def forward(self, inputs, mask=None):
        # inputs: [batch_size, seq_len, embedding_dim]
        batch_size, seq_len, _ = inputs.shape
        
        # 展平输入
        flat_inputs = inputs.view(-1, self.embedding_dim)
        
        # 计算输入与码本向量的L2距离
        distances = (torch.sum(flat_inputs**2, dim=1, keepdim=True) 
                    + torch.sum(self.embeddings.weight**2, dim=1)
                    - 2 * torch.matmul(flat_inputs, self.embeddings.weight.t()))
        
        # 获取最近邻的码本索引
        encoding_indices = torch.argmin(distances, dim=1)
        quantized = self.embeddings(encoding_indices).view(batch_size, seq_len, self.embedding_dim)
        
        # 直通估计器：前向传播使用量化值，反向传播使用原始输入
        quantized_st = inputs + (quantized - inputs).detach()
        
        # 计算VQ损失
        e_latent_loss = F.mse_loss(quantized.detach(), inputs)
        q_latent_loss = F.mse_loss(quantized, inputs.detach())
        vq_loss = q_latent_loss + self.commitment_cost * e_latent_loss
        
        # 如果提供了mask，则只计算非填充部分的损失
        if mask is not None:
            non_pad_elements = mask.sum()
            if non_pad_elements > 0:
                vq_loss = vq_loss * (inputs.numel() / non_pad_elements)
        
        # 返回量化结果、损失和编码索引
        return quantized_st, vq_loss, encoding_indices.view(batch_size, seq_len)

class ProteinVQVAEEncoder(nn.Module):
    """蛋白质VQ-VAE编码器"""
    def __init__(self, num_atom_types, embedding_dim=128, num_embeddings=512, 
                 hidden_dims=[256, 256], commitment_cost=0.25):
        super().__init__()
        self.embedding_dim = embedding_dim
        
        # 原子类型嵌入层
        self.atom_type_embedding = nn.Embedding(num_atom_types + 1, embedding_dim)  # +1 for padding
        
        # 坐标处理层
        self.coord_layer = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(embedding_dim * 2, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # Transformer编码器捕获原子间关系
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, 
            nhead=8,
            dim_feedforward=512,
            dropout=0.1,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=4)
        
        # 输出投影层
        self.output_proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # VQ层
        self.vq_layer = VectorQuantizer(num_embeddings, embedding_dim, commitment_cost)

    def forward(self, atom_coords, atom_types, mask=None):
        # 原子类型嵌入 [B, N, E]
        type_embedded = self.atom_type_embedding(atom_types)
        
        # 坐标嵌入 [B, N, E]
        coord_embedded = self.coord_layer(atom_coords)
        
        # 拼接特征并融合 [B, N, 2E] -> [B, N, E]
        combined = torch.cat([type_embedded, coord_embedded], dim=-1)
        fused = self.fusion_layer(combined)
        
        # Transformer编码 [B, N, E]
        # 创建padding mask（需要反转，因为Transformer期望True表示被mask）
        if mask is not None:
            padding_mask = ~mask
        else:
            padding_mask = None
            
        features = self.transformer_encoder(fused, src_key_padding_mask=padding_mask)
        
        # 输出投影
        features = self.output_proj(features)
        
        # 向量量化
        quantized, vq_loss, encoding_indices = self.vq_layer(features, mask)
        
        return quantized, vq_loss, encoding_indices

# 3. 训练函数
def train_vqvae(pdb_dir, epochs=50, batch_size=4, max_atoms=500, device='cuda'):
    # 创建数据集
    dataset = ProteinDataset(pdb_dir, max_atoms=max_atoms)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    
    # 初始化模型
    num_atom_types = len(dataset.ATOM_TYPE_MAP) + 1  # +1 for unknown/padding
    model = ProteinVQVAEEncoder(
        num_atom_types=num_atom_types,
        embedding_dim=512,
        num_embeddings=8192,  # 码本大小
        hidden_dims=[256, 256],
        commitment_cost=0.25
    ).to(device)
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=1e-3)
    
    # 训练循环
    losses = []
    for epoch in range(epochs):
        epoch_loss = 0.0
        model.train()
        # 在 train_vqvae 函数的训练循环中添加
        unique_codes = []
        for batch in tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}"):
            # 移动到设备
            coords = batch['coords'].to(device)
            types = batch['types'].to(device)
            mask = batch['mask'].to(device)
            
            # 前向传播
            _, vq_loss, _ = model(coords, types, mask)
            
            # 计算损失
            loss = vq_loss
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            torch.cuda.empty_cache()

            # 监控码本使用
            with torch.no_grad():
                _, _, indices = model(coords, types, mask)
                valid_indices = indices[mask.bool()].cpu().numpy()
                unique_codes.append(np.unique(valid_indices))
    
        # 每个epoch结束时打印
        all_codes = np.concatenate(unique_codes)
        usage = len(np.unique(all_codes)) / model.codebook_size * 100
        print(f"Batch {batch_idx}: Codebook usage: {usage:.2f}%")
        unique_codes = []  # 重置
        
        avg_loss = epoch_loss / len(dataloader)
        losses.append(avg_loss)
        print(f"Epoch {epoch+1}/{epochs} - Loss: {avg_loss:.4f}")
    
    # 绘制训练损失
    plt.plot(losses)
    plt.xlabel("Epoch")
    plt.ylabel("VQ Loss")
    plt.title("VQ-VAE Training Loss")
    plt.savefig("vqvae_training_loss.png")
    plt.close()
    
    return model

# 4. 使用训练好的模型编码蛋白质
def encode_proteins(model, pdb_dir, max_atoms=500, device='cuda'):
    dataset = ProteinDataset(pdb_dir, max_atoms=max_atoms)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)  # 单样本批次
    
    all_codes = []
    model.eval()
    unique_codes = []
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Encoding proteins"):
            coords = batch['coords'].to(device)
            types = batch['types'].to(device)
            mask = batch['mask'].to(device)
            
            # 打印输入结构
            print("\nInput Structure:")
            print(f"coords shape: {coords.shape}")
            print(f"types shape: {types.shape}")
            print(f"mask shape: {mask.shape}")
            print(f"real data:{len(coords[mask.bool()])}")
            print(f"First 5 coords: {coords[mask.bool()]}")
            print(f"First 5 types: {types[mask.bool()]}")
            print(f"First 5 mask: {mask[0, :5]}")
            
            # 获取编码
            _, _, encoding_indices = model(coords, types, mask)
            
            # 提取非填充部分的编码
            valid_indices = encoding_indices[mask.bool()].cpu().numpy()
            all_codes.append(valid_indices)

            unique_codes.append(np.unique(valid_indices))
            
            # 打印输出结构
            print("\nOutput Structure:")
            print(f"encoding_indices shape: {encoding_indices.shape}")
            print(f"First 5 encoding indices: {encoding_indices[0, :5]}")
            
    print(f"Generate unique code lenth: {len(unique_codes)}")
    return all_codes

# 主函数
if __name__ == "__main__":
    # 配置
    # PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"  # 替换为你的PDB文件目录
    PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"  # 替换为你的PDB文件目录
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {DEVICE}")
    
    # # 步骤1: 训练VQ-VAE
    # print("Training VQ-VAE model...")
    # trained_model = train_vqvae(
    #     pdb_dir=PDB_DIR,
    #     epochs=30,
    #     batch_size=4,
    #     max_atoms=30000,  # 每个蛋白质最多500个原子
    #     device=DEVICE
    # )
    
    # # 保存模型
    # torch.save(trained_model.state_dict(), "protein_vqvae_encoder.pth")

    # 加载预训练权重以确定正确的参数
    checkpoint = torch.load("./protein_vqvae_encoder_ver1.pth", map_location=DEVICE)
    # 从checkpoint推断参数
    atom_embedding_shape = checkpoint['atom_type_embedding.weight'].shape
    vq_embedding_shape = checkpoint['vq_layer.embeddings.weight'].shape
    num_atom_types = atom_embedding_shape[0] - 1  # -1 for padding
    embedding_dim = atom_embedding_shape[1]
    num_embeddings = vq_embedding_shape[0]
    print(f"Detected model parameters from checkpoint:")
    print(f"  num_atom_types: {num_atom_types}")
    print(f"  embedding_dim: {embedding_dim}")
    print(f"  num_embeddings: {num_embeddings}")
    # 初始化VQVAE编码器，使用从checkpoint推断的参数
    vqvae_encoder = ProteinVQVAEEncoder(
        num_atom_types=num_atom_types,
        embedding_dim=embedding_dim,
        num_embeddings=num_embeddings,
        commitment_cost=0.25
    )
    # 加载预训练权重
    vqvae_encoder.load_state_dict(checkpoint)
    vqvae_encoder.to(DEVICE)
    vqvae_encoder.eval()
    print("✅ VQVAE encoder loaded successfully")



    # 步骤2: 使用训练好的模型编码蛋白质
    print("\nEncoding proteins using trained model...")
    protein_codes = encode_proteins(
        model=vqvae_encoder,
        pdb_dir=PDB_DIR,
        max_atoms=30000,
        device=DEVICE
    )
    
    # 打印一些编码结果
    print("\nEncoding examples:")
    for i, codes in enumerate(protein_codes[:10]):
        print(f"Protein {i+1}: {len(codes)} atoms")
        print(f"First 10 codes: {codes[:100]}")
        print(f"Unique codes: {np.unique(codes).size} distinct tokens")