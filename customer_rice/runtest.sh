﻿#!/usr/bin/bash

# 打印当前工作目录
echo $(pwd)

# 设置环境变量
export PYTHONPATH=$(pwd)
export PATH=/usr/local/cuda-12.4/bin:$PATH
export LD_LIBRARY_PATH=/usr/local/cuda-12.4/lib64:$LD_LIBRARY_PATH

# 打印环境变量
echo $PYTHONPATH $PATH $LD_LIBRARY_PATH

# 定义数据集名称
dataset_names=("DeepPeppi" "Ar" "Rice")

# 定义是否平衡数据集和是否将剩余数据添加到验证集的参数
is_balance="True"
add_remaining_to_val="True"

# 获取当前时间戳，用于日志和模型文件名
timestamp=$(date +"%Y%m%d_%H%M%S")

# 遍历数据集名称，执行脚本
for dataset_name in "${dataset_names[@]}"; do
    # 定义日志文件名和模型文件名
    log_file="test_${dataset_name}_isbalance_${is_balance}_addremaining_${add_remaining_to_val}_${timestamp}.log"
    model_file="model_${dataset_name}_isbalance_${is_balance}_addremaining_${add_remaining_to_val}_${timestamp}.pth"

    # 执行 Python 脚本，并将输出重定向到日志文件
    PYTHONPATH=$(pwd) CUDA_VISIBLE_DEVICES=1 python test_alphafold2.py \
        --model_save "$model_file" \
        --dataset_name "$dataset_name" \
        --is_balance "$is_balance" \
        --add_remaining_to_val "$add_remaining_to_val" \
        > "$log_file" 2>&1

    # 打印执行信息
    echo "执行完成：数据集 $dataset_name，is_balance $is_balance，add_remaining_to_val $add_remaining_to_val，日志文件 $log_file，模型文件 $model_file"
done