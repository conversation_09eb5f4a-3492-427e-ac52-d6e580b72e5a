﻿import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser, Select
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
import matplotlib.pyplot as plt
from tqdm import tqdm

# 忽略PDB解析中的一些警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)

# 标准氨基酸残基类型映射
STANDARD_AMINO_ACIDS = [
    'ALA', 'ARG', 'ASN', 'ASP', 'CYS', 'GLN', 'GLU', 'GLY', 'HIS', 'ILE',
    'LEU', 'LYS', 'MET', 'PHE', 'PRO', 'SER', 'THR', 'TRP', 'TYR', 'VAL'
]

# 1. PDB文件解析器（残基级别）
class ProteinDataset(Dataset):
    """从PDB文件加载蛋白质残基数据的Dataset类"""
    
    def __init__(self, pdb_dir, max_residues=300):
        """
        pdb_dir: 包含PDB文件的目录
        max_residues: 每个蛋白质的最大残基数（填充或截断）
        """
        self.pdb_files = [os.path.join(pdb_dir, f) for f in os.listdir(pdb_dir) 
                          if f.endswith('.pdb')]
        self.max_residues = max_residues
        self.parser = PDBParser(QUIET=True)
        
        # 创建残基类型映射
        self.RESIDUE_TYPE_MAP = {res: i+1 for i, res in enumerate(STANDARD_AMINO_ACIDS)}
        self.RESIDUE_TYPE_MAP['UNK'] = 0  # 未知残基类型
        self.num_residue_types = len(self.RESIDUE_TYPE_MAP)
        
        print(f"Found {len(self.pdb_files)} PDB files. Processing...")
        
    def __len__(self):
        return len(self.pdb_files)
    
    def __getitem__(self, idx):
        pdb_file = self.pdb_files[idx]
        
        try:
            # 解析PDB文件
            structure = self.parser.get_structure("protein", pdb_file)
            
            # 提取所有残基
            residues = []
            for model in structure:
                for chain in model:
                    for residue in chain:
                        # 只处理氨基酸残基
                        if residue.get_resname().strip() in STANDARD_AMINO_ACIDS:
                            residues.append(residue)
            
            # 提取残基的CA原子坐标和类型
            coords = []
            types = []
            valid_residues = []
            
            for residue in residues:
                resname = residue.get_resname().strip()
                # 获取CA原子
                if 'CA' in residue:
                    ca_atom = residue['CA']
                    coord = ca_atom.get_coord()
                    residue_type = self.RESIDUE_TYPE_MAP.get(resname, 0)  # 0 for unknown
                    
                    coords.append(coord)
                    types.append(residue_type)
                    valid_residues.append(residue)
            
            # 转换为numpy数组
            coords = np.array(coords, dtype=np.float32)
            types = np.array(types, dtype=np.int64)
            num_residues = len(coords)
            
            # 随机选择最大残基数（如果残基数超过限制）
            if num_residues > self.max_residues:
                indices = np.random.choice(num_residues, self.max_residues, replace=False)
                coords = coords[indices]
                types = types[indices]
                num_residues = self.max_residues
            
            # 填充到最大残基数
            if num_residues < self.max_residues:
                pad_size = self.max_residues - num_residues
                coords = np.pad(coords, ((0, pad_size), (0, 0)), 
                                mode='constant', constant_values=0)
                types = np.pad(types, (0, pad_size), mode='constant', 
                              constant_values=0)  # 0 for padding
            
            # 坐标归一化（每个蛋白质单独归一化）
            coords_mean = coords.mean(axis=0)
            coords_std = coords.std(axis=0) + 1e-8  # 避免除以零
            coords = (coords - coords_mean) / coords_std
            
            # 创建mask（1表示真实残基，0表示填充）
            mask = np.zeros(self.max_residues, dtype=bool)
            mask[:num_residues] = True
            
            return {
                'coords': torch.tensor(coords, dtype=torch.float32),
                'types': torch.tensor(types, dtype=torch.long),
                'mask': torch.tensor(mask, dtype=torch.bool),
                'num_residues': num_residues
            }
        
        except Exception as e:
            print(f"Error processing {pdb_file}: {str(e)}")
            # 返回空数据
            return {
                'coords': torch.zeros((self.max_residues, 3), dtype=torch.float32),
                'types': torch.zeros(self.max_residues, dtype=torch.long),
                'mask': torch.zeros(self.max_residues, dtype=torch.bool),
                'num_residues': 0
            }

# 2. VQ-VAE模型定义（残基级别）
class VectorQuantizer(nn.Module):
    """向量量化层"""
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.embedding_dim = embedding_dim
        self.num_embeddings = num_embeddings
        self.commitment_cost = commitment_cost
        
        # 初始化码本
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1/num_embeddings, 1/num_embeddings)

    def forward(self, inputs, mask=None):
        # inputs: [batch_size, seq_len, embedding_dim]
        batch_size, seq_len, _ = inputs.shape
        
        # 展平输入
        flat_inputs = inputs.view(-1, self.embedding_dim)
        
        # 计算输入与码本向量的L2距离
        distances = (torch.sum(flat_inputs**2, dim=1, keepdim=True) 
                    + torch.sum(self.embeddings.weight**2, dim=1)
                    - 2 * torch.matmul(flat_inputs, self.embeddings.weight.t()))
        
        # 获取最近邻的码本索引
        encoding_indices = torch.argmin(distances, dim=1)
        quantized = self.embeddings(encoding_indices).view(batch_size, seq_len, self.embedding_dim)
        
        # 直通估计器：前向传播使用量化值，反向传播使用原始输入
        quantized_st = inputs + (quantized - inputs).detach()
        
        # 计算VQ损失
        e_latent_loss = F.mse_loss(quantized.detach(), inputs)
        q_latent_loss = F.mse_loss(quantized, inputs.detach())
        vq_loss = q_latent_loss + self.commitment_cost * e_latent_loss
        
        # 如果提供了mask，则只计算非填充部分的损失
        if mask is not None:
            non_pad_elements = mask.sum()
            if non_pad_elements > 0:
                vq_loss = vq_loss * (inputs.numel() / (non_pad_elements * self.embedding_dim))
        
        # 返回量化结果、损失和编码索引
        return quantized_st, vq_loss, encoding_indices.view(batch_size, seq_len)

class ProteinResidueVQVAEEncoder(nn.Module):
    """蛋白质残基级别VQ-VAE编码器"""
    def __init__(self, num_residue_types, embedding_dim=128, num_embeddings=512, 
                 hidden_dims=[256, 256], commitment_cost=0.25):
        super().__init__()
        self.embedding_dim = embedding_dim
        
        # 残基类型嵌入层 (0: padding, 1-20: standard amino acids, 21: unknown)
        self.residue_type_embedding = nn.Embedding(num_residue_types, embedding_dim)
        
        # 坐标处理层
        self.coord_layer = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(embedding_dim * 2, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # Transformer编码器捕获残基间关系
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, 
            nhead=8,
            dim_feedforward=512,
            dropout=0.1,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=4)
        
        # 输出投影层
        self.output_proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        
        # VQ层
        self.vq_layer = VectorQuantizer(num_embeddings, embedding_dim, commitment_cost)

    def forward(self, residue_coords, residue_types, mask=None):
        # 残基类型嵌入 [B, N, E]
        type_embedded = self.residue_type_embedding(residue_types)
        
        # 坐标嵌入 [B, N, E]
        coord_embedded = self.coord_layer(residue_coords)
        
        # 拼接特征并融合 [B, N, 2E] -> [B, N, E]
        combined = torch.cat([type_embedded, coord_embedded], dim=-1)
        fused = self.fusion_layer(combined)
        
        # Transformer编码 [B, N, E]
        # 创建padding mask（需要反转，因为Transformer期望True表示被mask）
        if mask is not None:
            padding_mask = ~mask
        else:
            padding_mask = None
            
        features = self.transformer_encoder(fused, src_key_padding_mask=padding_mask)
        
        # 输出投影
        features = self.output_proj(features)
        
        # 向量量化
        quantized, vq_loss, encoding_indices = self.vq_layer(features, mask)
        
        return quantized, vq_loss, encoding_indices

# 3. 训练函数
def train_vqvae(pdb_dir, epochs=50, batch_size=4, max_residues=300, device='cuda'):
    # 创建数据集
    dataset = ProteinDataset(pdb_dir, max_residues=max_residues)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    
    # 初始化模型
    num_residue_types = dataset.num_residue_types
    model = ProteinResidueVQVAEEncoder(
        num_residue_types=num_residue_types,
        embedding_dim=256,
        num_embeddings=1024,  # 码本大小
        hidden_dims=[256, 256],
        commitment_cost=0.25
    ).to(device)
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=1e-3)
    
    # 训练循环
    losses = []
    for epoch in range(epochs):
        epoch_loss = 0.0
        model.train()
        unique_codes = []
        
        for batch in tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}"):
            # 移动到设备
            coords = batch['coords'].to(device)
            types = batch['types'].to(device)
            mask = batch['mask'].to(device)
            
            # 前向传播
            _, vq_loss, _ = model(coords, types, mask)
            
            # 计算损失
            loss = vq_loss
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            torch.cuda.empty_cache()

            # 监控码本使用
            with torch.no_grad():
                _, _, indices = model(coords, types, mask)
                valid_indices = indices[mask].cpu().numpy()
                unique_codes.append(np.unique(valid_indices))
    
        # 每个epoch结束时打印
        all_codes = np.concatenate(unique_codes) if unique_codes else np.array([])
        usage = len(np.unique(all_codes)) / model.vq_layer.num_embeddings * 100 if len(all_codes) > 0 else 0
        print(f"Epoch {epoch+1}/{epochs}: Codebook usage: {usage:.2f}%")
        
        avg_loss = epoch_loss / len(dataloader)
        losses.append(avg_loss)
        print(f"Epoch {epoch+1}/{epochs} - Loss: {avg_loss:.4f}")
    
    # 绘制训练损失
    plt.plot(losses)
    plt.xlabel("Epoch")
    plt.ylabel("VQ Loss")
    plt.title("Residue-level VQ-VAE Training Loss")
    plt.savefig("residue_vqvae_training_loss.png")
    plt.close()
    
    return model

# 4. 使用训练好的模型编码蛋白质
def encode_proteins(model, pdb_dir, max_residues=300, device='cuda'):
    dataset = ProteinDataset(pdb_dir, max_residues=max_residues)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)  # 单样本批次
    
    all_codes = []
    model.eval()
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Encoding proteins"):
            coords = batch['coords'].to(device)
            types = batch['types'].to(device)
            mask = batch['mask'].to(device)
            num_residues = batch['num_residues'].item()
            
            # 获取编码
            _, _, encoding_indices = model(coords, types, mask)
            
            # 提取非填充部分的编码
            valid_indices = encoding_indices[0, :num_residues].cpu().numpy()
            all_codes.append(valid_indices)
    
    return all_codes

# 主函数
if __name__ == "__main__":
    # 配置
    PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"  # 替换为你的PDB文件目录
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"Using device: {DEVICE}")
    
    # 步骤1: 训练VQ-VAE
    print("Training Residue-level VQ-VAE model...")
    trained_model = train_vqvae(
        pdb_dir=PDB_DIR,
        epochs=30,
        batch_size=8,
        max_residues=300,  # 每个蛋白质最多300个残基
        device=DEVICE
    )
    
    # 保存模型
    torch.save(trained_model.state_dict(), "protein_residue_vqvae_encoder.pth")
    
    # 步骤2: 使用训练好的模型编码蛋白质
    print("\nEncoding proteins using trained model...")
    protein_codes = encode_proteins(
        model=trained_model,
        pdb_dir=PDB_DIR,
        max_residues=300,
        device=DEVICE
    )
    
    # 打印一些编码结果
    print("\nEncoding examples:")
    for i, codes in enumerate(protein_codes[:5]):
        print(f"Protein {i+1}: {len(codes)} residues")
        print(f"First 10 codes: {codes[:10]}")
        print(f"Unique codes: {np.unique(codes).size} distinct tokens")
        print(f"Code distribution: {np.bincount(codes)}")
        print()