﻿import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader, Subset
import pandas as pd
import os
import pickle
from sklearn.model_selection import train_test_split
import numpy as np
from load_data import load_pairs, load_sequences,load_sequences2
import warnings
import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_sequence
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import os
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix
from evaluate_model import evaluate_model
from test_alphafold2 import ProteinPeptideDataset,InteractionTransformer
if __name__ == "__main__":

    # 创建数据集
    full_dataset = ProteinPeptideDataset(positive_pairs, negative_pairs, feature_dir)
    
    # 获取输入维度
    sample_feat, _ = full_dataset[0]
    input_dim = sample_feat.size(-1) - 1
    
    # # 分层划分数据集
    # indices = np.arange(len(full_dataset))
    # labels = full_dataset.processed_pairs['label']
    
    # # 第一次划分：训练集（80%）和临时集（20%）
    # train_idx, temp_idx = train_test_split(
    #     indices,
    #     test_size=0.2,
    #     stratify=labels,
    #     random_state=42
    # )
    # # 确保 labels[temp_idx] 的索引与 temp_idx 一致
    # temp_labels = labels.iloc[temp_idx].reset_index(drop=True)
    # # 第二次划分：测试集（50%临时集）和验证集（剩余50%）
    # test_idx, val_idx = train_test_split(
    #     temp_idx,
    #     test_size=0.5,
    #     stratify=temp_labels,
    #     random_state=42
    # )
    
    # # 创建数据加载器
    # train_loader = DataLoader(
    #     Subset(full_dataset, train_idx),
    #     batch_size=32,
    #     shuffle=True,
    #     collate_fn=collate_fn,
    #     num_workers=4
    # )
    # val_loader = DataLoader(
    #     Subset(full_dataset, val_idx),
    #     batch_size=32,
    #     shuffle=True,
    #     collate_fn=collate_fn,
    #     num_workers=4
    # )
    test_loader = DataLoader(
        full_dataset,
        batch_size=32,
        shuffle=False,
        collate_fn=collate_fn,
        num_workers=4
    )
    
    # 初始化模型
    model = InteractionTransformer(input_dim=input_dim)
    # 加载最佳模型并评估测试集
    model.load_state_dict(torch.load(model_save))
    
    
    print("\nFinal Evaluation on Test Set:")
    evaluate_model(model, test_loader)
    print(f"min_samples {min_samples}")