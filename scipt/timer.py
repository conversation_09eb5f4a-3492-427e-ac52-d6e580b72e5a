import schedule
import time
from a3 import main


def job():
    # 你的任务逻辑
    print("任务执行于:", time.strftime("%Y-%m-%d %H:%M:%S"))


if __name__ == "__main__":
    # 设置每天北京时间下午 2:50 执行任务
    # schedule.every().day.at("14:50:00").do(job)
    schedule.every().day.at("21:59:45").do(main)
    # schedule.every().day.at("10:22:00").do(main)

    while True:
        # 检查是否有任务需要执行
        schedule.run_pending()
        # 休息 60 秒钟，避免 CPU 占用过高
        time.sleep(1)
        print("HEARTBEAT", time.strftime("%Y-%m-%d %H:%M:%S"))
