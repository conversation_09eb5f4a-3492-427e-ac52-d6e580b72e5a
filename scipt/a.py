import time
import json
import threading
import requests
from datetime import datetime, timedelta

# 配置参数
query_interval = 0  # 两次查询间隔(s)
login_url = "http://cas.hnu.edu.cn/cas/login？service=http://cas.hnu.edu.cn/system/login/login.zf"
Cookie = "PHPSESSID=ST-5635134-9baF1IHaT2l6HCzeRbzd-zfsoftcom; vjuid=283272; vjvd=8a508283394996a486ffb5752d5ae90c; vt=266710596; cas_ticket=ST-5635134-9baF1IHaT2l6HCzeRbzd-zfsoft.com"

# 多场馆配置
VENUES = {
    "南校篮球馆": {
        "resource_id": "85",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场"]
    },
    "北校篮球馆": {
        "resource_id": "84",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    },
    "楼下篮球馆": {
        "resource_id": "57",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    }
}

# 用户配置：指定要预约的场馆和场地
TARGET_VENUES = ["南校篮球馆","楼下篮球馆"]  # 可以指定多个场馆
# TARGET_COURTS = ["1号场", "2号场"]  # 可以指定多个场地
TARGET_TIME_SLOTS = ["20"]  # 指定时间段，空列表表示所有时间段
MAX_PERIODS_PER_BOOKING = 2  # 单次预约最大时间段数量（系统限制）

current_date = datetime.now()
target_date = (current_date + timedelta(days=1)).strftime("%Y-%m-%d")
TARGET_DATE = target_date  # 目标日期，格式：YYYY-MM-DD

print("=== 多场馆预约系统配置 ===")
print(f"目标场馆: {TARGET_VENUES}")
# print(f"目标场地: {TARGET_COURTS}")
print(f"目标时间段: {TARGET_TIME_SLOTS if TARGET_TIME_SLOTS else '所有时间段'}")
print(f"目标日期: {TARGET_DATE}")
print(f"单次预约最大时间段数: {MAX_PERIODS_PER_BOOKING}")
print("=" * 40)

def get_court_data_from_api(resource_id, date, venue_name):
    """
    从API获取场地数据
    """
    url = f"https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": resource_id,
        "start_time": date,
        "end_time": date
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": Cookie,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest"
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()

        print(f"[{venue_name}] API返回状态: {data.get('m', '未知')}")

        # 解析API返回的数据，构建court_data格式
        court_data = {}
        if data.get('e') == 0 and 'd' in data:
            # API返回的数据结构: {"e": 0, "m": "操作成功", "d": {"367": [...]}}
            for items in data['d'].values():
                for item in items:
                    court_name = item.get('abscissa', '')  # 场地名称在abscissa字段
                    time_slot = item.get('yaxis', '')      # 时间段在yaxis字段
                    time_id = item.get('time_id')          # 时间ID
                    sub_id = item.get('sub_id')            # 子资源ID
                    row = item.get('row')            # 子资源ID
                    status = row["status"]
                    
                    if status != 5:
                        continue
                    
                    time_slots = time_slot.split('-')
                    is_time = False
                    for time in TARGET_TIME_SLOTS:
                        # print(time_slots)
                        for time_slot in time_slots:
                            if time in time_slot:
                                is_time = True
                    
                    if is_time == False:
                        continue
                    # print(court_name)
                    if court_name not in court_data:
                        court_data[court_name] = []
                    court_data[court_name].append((time_id, sub_id, resource_id))
                    
                    # # 检查是否是目标场地和时间段
                    # if any(target in court_name for target in TARGET_COURTS):
                    #     # 如果指定了时间段，只获取指定时间段的数据
                    #     if TARGET_TIME_SLOTS and time_slot not in TARGET_TIME_SLOTS:
                    #         continue

                    #     # 提取场地编号，如"篮球馆1号" -> "1号场"
                    #     court_number = None
                    #     for target in TARGET_COURTS:
                    #         if target.replace('号场', '号') in court_name:
                    #             court_number = target
                    #             break

                    #     if court_number and time_id and sub_id:
                    #         # 为场地名称添加场馆前缀以区分不同场馆
                    #         full_court_name = f"{venue_name}-{court_number}"
                    #         if full_court_name not in court_data:
                    #             court_data[full_court_name] = []
                    #         court_data[full_court_name].append((time_id, sub_id, resource_id))
                    #         print(f"[{venue_name}] 找到场地: {court_number}, 时间: {time_slot}, time_id: {time_id}, sub_id: {sub_id}")
        # print(court_data)
        return court_data
    except Exception as e:
        print(f"[{venue_name}] 获取场地数据失败: {e}")
        return {}

def get_all_venues_data():
    """
    获取所有指定场馆的数据
    """
    all_court_data = {}

    for venue_name in TARGET_VENUES:
        if venue_name not in VENUES:
            print(f"警告: 未知场馆 {venue_name}")
            continue

        venue_config = VENUES[venue_name]
        resource_id = venue_config["resource_id"]

        print(f"正在获取 {venue_name} (ID: {resource_id}) 的场地数据...")
        venue_data = get_court_data_from_api(resource_id, TARGET_DATE, venue_name)
        print(venue_data)
        all_court_data.update(venue_data)

    if not all_court_data:
        print("未能从任何场馆获取到有效数据，使用默认配置")
        # 返回默认数据
        all_court_data = {
            "南校篮球馆-1号场": [(4480, 21052, "85"), (4481, 21051, "85")],
            "南校篮球馆-2号场": [(4480, 21066, "85"), (4481, 21065, "85")],
        }
    pop_keys = []
    for key ,value in all_court_data.items():
        # print(len(value))
        if len(value) == 1:
            # print(key)
            pop_keys.append(key)
    for key in pop_keys:
        print("YICCHU"+key)
        all_court_data.pop(key)
    return all_court_data

# 动态获取所有场馆的court_data
court_data = get_all_venues_data()

court_numbers = list(court_data.keys())  # 目标场号列表
print(f"获取到的场地: {court_numbers}")

# 共享状态
reserved_times = set()  # 用于跟踪已预约的时间段
lock = threading.Lock()  # 创建一个锁
stop_event = threading.Event()  # 用于通知线程停止

# ==============================================================

def launch(court_number):
    """
    尝试预约指定场地的时间段
    """
    url = "https://eportal.hnu.edu.cn/site/reservation/launch"
    target_date = TARGET_DATE  # 使用配置的目标日期

    # 获取该场地的resource_id和时间段数据
    resource_id = None
    periods_to_book = court_data[court_number]  # 限制预约数量
    json_data = []

    for time_id, sub_id, r_id in periods_to_book:
        if resource_id is None:
            resource_id = r_id  # 获取resource_id
        json_data.append({
            "date": target_date,
            "period": time_id,
            "sub_resource_id": sub_id
        })

    print(f"[{court_number}] 预约数据 ({len(json_data)}/{len(court_data[court_number])} 个时间段): {json.dumps(json_data, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            timeout=10,
            headers={
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Cookie": Cookie,
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Requested-With":"XMLHttpRequest"
            },
            data={
                "resource_id": resource_id,
                "code": "",
                "remarks": "",
                "deduct_num": "",
                "data": json.dumps(json_data),
            },
        )

        response.raise_for_status()
        data = response.json()

        if data["m"] == "操作成功":
            print(f"✅ 预约成功！场号: {court_number}, 时间段数: {len(json_data)}")
            return True
        else:
            print(f"❌ 预约失败，场号: {court_number}, 原因: {data['m']}")
            return False
    except Exception as e:
        print(f"❌ [{court_number}] 发生错误: {e}")
        return False


def worker(court_number):
    """
    线程工作函数
    """
    global reserved_times

    while not stop_event.is_set():
        with lock:
            # 检查是否已预约该场地的时间段
            if court_number in reserved_times:
                print(f"场号 {court_number} 已被预约，跳过预约")
                return  # 如果已预约，不再继续

            # 预约该场地的所有时间段
            if launch(court_number):
                reserved_times.add(court_number)  # 记录已预约的场地

        time.sleep(query_interval)  # 等待指定时间后重试


def stop_after_timeout(timeout):
    """
    在指定时间后触发停止事件
    """
    time.sleep(timeout)
    stop_event.set()
    print("运行时间到达限制，所有线程即将停止并退出。")

def ticket():
    # 创建一个线程用于定时停止
    timeout = 120  # 设置运行时间为 2 分钟
    stop_thread = threading.Thread(target=stop_after_timeout, args=(timeout,))
    stop_thread.start()

    # 创建多个线程，每个线程负责一个场号
    threads = []
    for court_number in court_numbers:
        print(court_number)
        thread = threading.Thread(target=worker, args=(court_number,))
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    # 等待停止线程结束
    stop_thread.join()

    print("程序已退出。")
if __name__ == "__main__":
    ticket()
    # print(court_data)
    # print(court_numbers)