#!/usr/bin/env python3
"""
测试 a3.py 中的登录功能
"""

import sys
import os

# 添加当前目录到路径，以便导入 a3 模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from a3 import get_cookie_from_login, <PERSON><PERSON>

def test_login():
    """
    测试登录功能
    """
    print("=== 测试登录功能 ===")
    
    # 尝试登录
    success = get_cookie_from_login()
    
    if success:
        print("✅ 登录测试成功！")
        print(f"获取到的Cookie长度: {len(Cookie)}")
        print(f"Cookie前100个字符: {Cookie[:100]}...")
        return True
    else:
        print("❌ 登录测试失败！")
        return False

if __name__ == "__main__":
    test_login()
