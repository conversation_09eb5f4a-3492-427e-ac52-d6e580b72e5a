# QuinNet 数据集结构文档

## 数据格式概览

QuinNet 使用 PyTorch Geometric 的 Data 对象作为基础数据结构，支持多种量子化学和分子动力学数据集。

### 基础数据结构

```python
from torch_geometric.data import Data

data = Data(
    z=torch.Tensor,           # [N] 原子序数 (原子类型)
    pos=torch.Tensor,         # [N, 3] 原子坐标 (Å)
    y=torch.Tensor,           # [1] 或 [19] 目标性质
    dy=torch.Tensor,          # [N, 3] 原子力 (可选)
    batch=torch.Tensor,       # [N] 批次索引 (批处理时)
    
    # 可选字段
    q=torch.Tensor,           # [1] 总电荷
    s=torch.Tensor,           # [1] 自旋多重度
    edge_index=torch.Tensor,  # [2, E] 边索引 (某些数据集)
    edge_attr=torch.Tensor,   # [E, D] 边特征 (某些数据集)
)
```

## 支持的数据集

### 1. QM9 数据集

#### 数据集描述
- **规模**: 134,000个小分子 (最多9个重原子)
- **元素**: H, C, N, O, F
- **性质**: 19种量子化学性质
- **来源**: DFT计算 (B3LYP/6-31G(2df,p))

#### 数据结构
```python
# QM9 Data对象
data = Data(
    z=torch.LongTensor,       # [N] 原子序数 [1,6,7,8,9]
    pos=torch.FloatTensor,    # [N, 3] 3D坐标 (Å)
    x=torch.FloatTensor,      # [N, 11] 原子特征 (可选)
    edge_index=torch.LongTensor, # [2, E] 化学键连接
    edge_attr=torch.FloatTensor,  # [E, 4] 键类型特征
    y=torch.FloatTensor,      # [19] 19种性质
    name=str,                 # 分子名称
    idx=int,                  # 数据索引
)
```

#### 目标性质列表
```python
qm9_properties = {
    0: 'mu',          # 偶极矩 (D)
    1: 'alpha',       # 极化率 (Bohr^3)
    2: 'homo',        # HOMO能级 (Hartree)
    3: 'lumo',        # LUMO能级 (Hartree)
    4: 'gap',         # HOMO-LUMO间隙 (Hartree)
    5: 'r2',          # 电子空间范围 (Bohr^2)
    6: 'zpve',        # 零点振动能 (Hartree)
    7: 'U0',          # 内能 0K (Hartree)
    8: 'U',           # 内能 298.15K (Hartree)
    9: 'H',           # 焓 298.15K (Hartree)
    10: 'G',          # 自由能 298.15K (Hartree)
    11: 'Cv',         # 热容 298.15K (cal/mol/K)
    12: 'omega1',     # 最低振动频率 (cm^-1)
    13: 'omega2',     # 第二低振动频率 (cm^-1)
    14: 'omega3',     # 第三低振动频率 (cm^-1)
    15: 'omega4',     # 第四低振动频率 (cm^-1)
    16: 'omega5',     # 第五低振动频率 (cm^-1)
    17: 'omega6',     # 第六低振动频率 (cm^-1)
    18: 'omega7',     # 第七低振动频率 (cm^-1)
}
```

#### 原子参考能量
```python
# QM9原子参考能量 (Hartree)
atomref = {
    1: [-0.500273],    # H
    6: [-37.846772],   # C
    7: [-54.583861],   # N
    8: [-75.064579],   # O
    9: [-99.718730],   # F
}
```

#### 使用示例
```python
from src.datasets.qm9 import QM9

# 加载HOMO能级预测数据
dataset = QM9(root='./data/qm9', dataset_arg='homo')
print(f"数据集大小: {len(dataset)}")
print(f"目标性质: {dataset.label}")

# 获取单个样本
data = dataset[0]
print(f"原子数: {data.z.size(0)}")
print(f"坐标形状: {data.pos.shape}")
print(f"HOMO能级: {data.y.item():.6f} Hartree")
```

### 2. MD17 数据集

#### 数据集描述
- **规模**: 每个分子约1000个构型
- **分子**: 8个有机分子
- **计算方法**: DFT (PBE+vdW-TS)
- **用途**: 分子动力学轨迹，力场训练

#### 支持的分子
```python
available_molecules = [
    'aspirin',         # 阿司匹林 (21原子)
    'benzene',         # 苯 (12原子)
    'ethanol',         # 乙醇 (9原子)
    'malonaldehyde',   # 丙二醛 (9原子)
    'naphthalene',     # 萘 (18原子)
    'salicylic_acid',  # 水杨酸 (16原子)
    'toluene',         # 甲苯 (15原子)
    'uracil',          # 尿嘧啶 (12原子)
]
```

#### 数据结构
```python
# MD17 Data对象
data = Data(
    z=torch.LongTensor,       # [N] 原子序数
    pos=torch.FloatTensor,    # [N, 3] 原子坐标 (Å)
    y=torch.FloatTensor,      # [1] 总能量 (kcal/mol)
    dy=torch.FloatTensor,     # [N, 3] 原子力 (kcal/mol/Å)
)
```

#### 原始文件格式
```python
# molecule_dft.npz 文件内容
{
    'Z': np.array,      # [total_atoms] 所有构型的原子序数
    'R': np.array,      # [total_atoms, 3] 所有构型的坐标
    'E': np.array,      # [n_configs] 每个构型的能量
    'F': np.array,      # [total_atoms, 3] 所有构型的力
    'N': np.array,      # [1] 每个分子的原子数
}
```

#### 使用示例
```python
from src.datasets.md17 import MD17

# 加载阿司匹林数据
dataset = MD17(root='./data/md17', dataset_arg='aspirin')
print(f"构型数量: {len(dataset)}")

# 获取单个构型
data = dataset[0]
print(f"原子数: {data.z.size(0)}")
print(f"能量: {data.y.item():.6f} kcal/mol")
print(f"力的形状: {data.dy.shape}")
```

### 3. ProteinRef 数据集

#### 数据集描述
- **内容**: 小蛋白质的DFT计算数据
- **分子**: chignolin (10残基)
- **计算方法**: DFT
- **用途**: 蛋白质力场开发

#### 数据结构
```python
# ProteinRef Data对象
data = Data(
    z=torch.LongTensor,       # [N] 原子序数
    pos=torch.FloatTensor,    # [N, 3] 原子坐标 (Å)
    y=torch.FloatTensor,      # [1] 相对能量 (Hartree)
    dy=torch.FloatTensor,     # [N, 3] 原子力 (Hartree/Bohr)
)
```

#### 自能参考值
```python
self_energies = {
    1: -0.496665677271,    # H
    6: -37.8289474402,     # C  
    7: -54.5677547104,     # N
    8: -75.0321126521,     # O
    16: -398.063946327,    # S
}
```

#### 能量处理
```python
# 相对能量计算
def process_energy(total_energy, atomic_numbers):
    ref_energy = sum(self_energies[z.item()] for z in atomic_numbers)
    relative_energy = (total_energy - ref_energy) * Hartree
    return relative_energy
```

## 数据预处理

### 1. 坐标变换

#### 随机旋转
```python
from scipy.spatial.transform import Rotation

def random_rotation(pos):
    """应用随机旋转"""
    R = torch.tensor(Rotation.random().as_matrix(), dtype=torch.float)
    return torch.matmul(pos, R.T)
```

#### 中心化
```python
def center_coordinates(pos):
    """将分子中心化到原点"""
    center = pos.mean(dim=0, keepdim=True)
    return pos - center
```

### 2. 单位转换

#### 能量单位
```python
# 常用转换因子
HAR2EV = 27.211386246      # Hartree -> eV
KCALMOL2EV = 0.04336414    # kcal/mol -> eV
EV2KCALMOL = 23.060548     # eV -> kcal/mol

# 转换示例
energy_ev = energy_hartree * HAR2EV
energy_kcalmol = energy_ev * EV2KCALMOL
```

#### 力单位
```python
# 力单位转换
Hartree = 27.211386246     # Hartree -> eV
Bohr = 0.529177210903      # Bohr -> Angstrom

# Hartree/Bohr -> eV/Å
force_ev_ang = force_hartree_bohr * Hartree / Bohr
```

### 3. 数据标准化

#### 能量标准化
```python
def standardize_energy(energies):
    """标准化能量"""
    mean = energies.mean()
    std = energies.std()
    return (energies - mean) / std, mean, std
```

#### 力标准化
```python
def standardize_forces(forces):
    """标准化力"""
    # 通常不对力进行标准化，或使用能量的标准差
    return forces
```

## 批处理

### 1. 数据加载器配置

```python
from torch_geometric.data import DataLoader

# 创建数据加载器
loader = DataLoader(
    dataset,
    batch_size=32,
    shuffle=True,
    num_workers=4,
    pin_memory=True,
    follow_batch=['z'],  # 为z创建batch索引
)
```

### 2. 批次数据结构

```python
# 批处理后的数据
batch = Batch(
    z=torch.LongTensor,       # [total_atoms] 所有分子的原子
    pos=torch.FloatTensor,    # [total_atoms, 3] 所有原子坐标
    y=torch.FloatTensor,      # [batch_size, 1] 每个分子的性质
    dy=torch.FloatTensor,     # [total_atoms, 3] 所有原子的力
    batch=torch.LongTensor,   # [total_atoms] 原子到分子的映射
    ptr=torch.LongTensor,     # [batch_size+1] 分子边界指针
)
```

## 自定义数据集

### 1. 创建自定义数据集

```python
from torch_geometric.data import InMemoryDataset
import numpy as np

class CustomDataset(InMemoryDataset):
    def __init__(self, root, transform=None, pre_transform=None):
        super().__init__(root, transform, pre_transform)
        self.data, self.slices = torch.load(self.processed_paths[0])
    
    @property
    def raw_file_names(self):
        return ['custom_data.xyz', 'custom_energies.txt']
    
    @property
    def processed_file_names(self):
        return ['data.pt']
    
    def process(self):
        # 读取原始数据
        data_list = []
        
        # 处理每个分子
        for i in range(num_molecules):
            # 创建Data对象
            data = Data(
                z=torch.tensor(atomic_numbers[i], dtype=torch.long),
                pos=torch.tensor(coordinates[i], dtype=torch.float),
                y=torch.tensor([[energies[i]]], dtype=torch.float),
            )
            
            if self.pre_transform is not None:
                data = self.pre_transform(data)
                
            data_list.append(data)
        
        # 保存处理后的数据
        torch.save(self.collate(data_list), self.processed_paths[0])
```

### 2. 数据验证

```python
def validate_dataset(dataset):
    """验证数据集的完整性"""
    for i, data in enumerate(dataset):
        # 检查必需字段
        assert hasattr(data, 'z'), f"Sample {i} missing atomic numbers"
        assert hasattr(data, 'pos'), f"Sample {i} missing positions"
        
        # 检查维度一致性
        assert data.z.size(0) == data.pos.size(0), f"Sample {i} dimension mismatch"
        
        # 检查数据类型
        assert data.z.dtype == torch.long, f"Sample {i} wrong z dtype"
        assert data.pos.dtype == torch.float, f"Sample {i} wrong pos dtype"
        
        # 检查坐标范围 (合理性检查)
        assert data.pos.abs().max() < 100, f"Sample {i} unrealistic coordinates"
```

## 数据统计

### 1. 数据集统计信息

```python
def dataset_statistics(dataset):
    """计算数据集统计信息"""
    num_atoms = [data.z.size(0) for data in dataset]
    energies = [data.y.item() for data in dataset if hasattr(data, 'y')]
    
    stats = {
        'num_samples': len(dataset),
        'avg_atoms': np.mean(num_atoms),
        'min_atoms': np.min(num_atoms),
        'max_atoms': np.max(num_atoms),
        'energy_mean': np.mean(energies),
        'energy_std': np.std(energies),
        'energy_range': (np.min(energies), np.max(energies)),
    }
    
    return stats
```

### 2. 元素分布

```python
def element_distribution(dataset):
    """分析元素分布"""
    element_counts = {}
    
    for data in dataset:
        unique, counts = torch.unique(data.z, return_counts=True)
        for z, count in zip(unique, counts):
            z_val = z.item()
            if z_val not in element_counts:
                element_counts[z_val] = 0
            element_counts[z_val] += count.item()
    
    return element_counts
```

## 数据可视化

### 1. 分子结构可视化

```python
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def visualize_molecule(data, title="Molecule"):
    """可视化分子结构"""
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 原子颜色映射
    colors = {1: 'white', 6: 'black', 7: 'blue', 8: 'red', 9: 'green'}
    
    for i, (z, pos) in enumerate(zip(data.z, data.pos)):
        color = colors.get(z.item(), 'gray')
        ax.scatter(pos[0], pos[1], pos[2], c=color, s=100)
    
    ax.set_title(title)
    ax.set_xlabel('X (Å)')
    ax.set_ylabel('Y (Å)')
    ax.set_zlabel('Z (Å)')
    plt.show()
```

### 2. 数据分布可视化

```python
def plot_energy_distribution(dataset):
    """绘制能量分布"""
    energies = [data.y.item() for data in dataset if hasattr(data, 'y')]
    
    plt.figure(figsize=(10, 6))
    plt.hist(energies, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('Energy')
    plt.ylabel('Frequency')
    plt.title('Energy Distribution')
    plt.grid(True, alpha=0.3)
    plt.show()
```
