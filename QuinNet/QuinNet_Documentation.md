# QuinNet: Efficiently Incorporating Quintuple Interactions into Geometric Deep Learning Force Fields

## 项目概述

QuinNet 是一个等变图神经网络，能够高效地表达多体相互作用，最高支持五体相互作用，并达到从头算精度。该项目基于 NeurIPS 2023 论文实现，专门用于分子动力学模拟和量子化学计算。

### 核心特性
- **多体相互作用**: 支持最高五体相互作用的建模
- **等变性**: 保持旋转和平移等变性
- **高精度**: 达到从头算级别的精度
- **高效计算**: 优化的注意力机制和几何处理
- **多任务支持**: 能量预测、力预测、偶极矩等多种性质

## 系统架构

### 1. 模型架构概览

```
输入: 原子坐标 + 原子类型
  ↓
原子嵌入 (Embedding)
  ↓
邻居嵌入 (NeighborEmbedding)
  ↓
多层等变注意力 (EquivariantMultiHeadAttention) × N
  ↓
输出模块 (OutputModel)
  ↓
输出: 能量/力/其他性质
```

### 2. 核心组件

#### QuinNet_Module 主模型
```python
class QuinNet_Module(nn.Module):
    """QuinNet主要的等变Transformer架构"""
    
    def __init__(self,
                 hidden_channels=128,      # 隐藏层维度
                 num_layers=6,            # 注意力层数
                 num_rbf=50,              # 径向基函数数量
                 rbf_type='expnorm',      # 径向基函数类型
                 activation='silu',       # 激活函数
                 num_heads=8,             # 注意力头数
                 cutoff_upper=5.0,        # 截断距离
                 max_z=100):              # 最大原子序数
```

#### 等变多头注意力机制
```python
class EquivariantMultiHeadAttention(MessagePassing):
    """实现五体相互作用的等变注意力机制"""
    
    # 核心创新：五种不同的向量点积项
    # 1. vec_dot1: 标准向量点积
    # 2. vec_dot2: 向量投影后的模长平方
    # 3. vec_dot3: 边上的向量相互作用
    # 4. vec_dot4: 源节点向量的平方和
    # 5. vec_dot5: 边更新的向量相互作用
    # 6. vec_dot6: 复杂向量投影相互作用
```

## 数据格式详解

### 1. 输入数据格式

#### 分子数据结构 (PyTorch Geometric Data)
```python
data = Data(
    z=torch.Tensor,           # [N] 原子序数 (原子类型)
    pos=torch.Tensor,         # [N, 3] 原子坐标 (Å)
    y=torch.Tensor,           # [1] 目标能量 (eV 或其他单位)
    dy=torch.Tensor,          # [N, 3] 目标力 (eV/Å 或其他单位)
    batch=torch.Tensor,       # [N] 批次索引
    
    # 可选字段
    q=torch.Tensor,           # [1] 总电荷
    s=torch.Tensor,           # [1] 自旋多重度
)
```

#### 原子类型编码
```python
# 支持的原子类型 (原子序数)
supported_atoms = {
    1: 'H',   6: 'C',   7: 'N',   8: 'O',   9: 'F',
    16: 'S',  17: 'Cl', 35: 'Br', 53: 'I'
    # 最大支持到 max_z=100
}
```

### 2. 数据集格式

#### MD17 数据集
```python
# 文件结构: molecule_dft.npz
{
    'Z': np.array,      # [total_atoms] 所有构型的原子序数
    'R': np.array,      # [total_atoms, 3] 所有构型的原子坐标
    'E': np.array,      # [n_configs] 每个构型的能量
    'F': np.array,      # [total_atoms, 3] 所有构型的原子力
    'N': np.array,      # [1] 每个分子的原子数
}
```

#### QM9 数据集
```python
# 19个目标性质
qm9_properties = [
    'mu',           # 偶极矩 (D)
    'alpha',        # 极化率 (Bohr^3)
    'homo',         # HOMO能级 (Hartree)
    'lumo',         # LUMO能级 (Hartree)
    'gap',          # HOMO-LUMO间隙 (Hartree)
    'r2',           # 电子空间范围 (Bohr^2)
    'zpve',         # 零点振动能 (Hartree)
    'U0',           # 内能 0K (Hartree)
    'U',            # 内能 298.15K (Hartree)
    'H',            # 焓 298.15K (Hartree)
    'G',            # 自由能 298.15K (Hartree)
    'Cv',           # 热容 298.15K (cal/mol/K)
    'omega1',       # 最低振动频率 (cm^-1)
    # ... 等
]
```

#### 蛋白质数据集 (ProteinRef)
```python
# chignolin_dft.npz 格式
{
    'Z': np.array,      # 原子序数
    'R': np.array,      # 原子坐标
    'E': np.array,      # 能量
    'F': np.array,      # 力
    'N': np.array,      # 原子数
}

# 自能参考值
self_energies = {
    1: -0.496665677271,    # H
    6: -37.8289474402,     # C
    7: -54.5677547104,     # N
    8: -75.0321126521,     # O
    16: -398.063946327,    # S
}
```

## API 接口文档

### 1. 主要模型类

#### QuinNet 完整模型
```python
class QuinNet(nn.Module):
    """QuinNet完整模型，包含表示学习和输出模块"""
    
    def __init__(self,
                 representation_model,    # 表示学习模型
                 output_model,           # 输出模块
                 prior_model=None,       # 先验模型
                 reduce_op='add',        # 聚合操作
                 mean=None,              # 数据均值
                 std=None,               # 数据标准差
                 derivative=False):      # 是否计算导数
    
    def forward(self,
                z: Tensor,              # [N] 原子序数
                pos: Tensor,            # [N, 3] 原子坐标
                batch: Tensor = None,   # [N] 批次索引
                q: Tensor = None,       # [batch_size] 电荷
                s: Tensor = None        # [batch_size] 自旋
                ) -> Tuple[Tensor, Optional[Tensor]]:
        """
        返回:
            energy: [batch_size, 1] 预测能量
            forces: [N, 3] 预测力 (如果derivative=True)
        """
```

#### 创建模型函数
```python
def create_model(args, prior_model=None, mean=None, std=None):
    """
    根据配置创建QuinNet模型
    
    Args:
        args: 配置字典，包含所有模型参数
        prior_model: 先验模型 (可选)
        mean: 训练数据均值 (可选)
        std: 训练数据标准差 (可选)
        
    Returns:
        QuinNet: 完整的模型实例
    """
```

### 2. 数据加载接口

#### 数据模块
```python
class DataModule:
    """数据加载和预处理模块"""
    
    def __init__(self, args):
        """
        Args:
            args.dataset: 数据集名称 ['QM9', 'MD17', 'ProteinRef']
            args.dataset_root: 数据根目录
            args.dataset_arg: 数据集参数 (如分子名称)
            args.batch_size: 批次大小
            args.train_size: 训练集大小
            args.val_size: 验证集大小
            args.test_size: 测试集大小
        """
    
    def prepare_data(self):
        """下载和预处理数据"""
        
    def setup(self, stage=None):
        """设置训练/验证/测试数据集"""
        
    def train_dataloader(self):
        """返回训练数据加载器"""
        
    def val_dataloader(self):
        """返回验证数据加载器"""
        
    def test_dataloader(self):
        """返回测试数据加载器"""
```

#### 数据集类
```python
class QM9(QM9_geometric):
    """QM9数据集包装器"""
    
    def __init__(self, root, dataset_arg=None, **kwargs):
        """
        Args:
            root: 数据根目录
            dataset_arg: 目标性质名称
        """
    
    def get_atomref(self, max_z=100):
        """获取原子参考能量"""

class MD17(InMemoryDataset):
    """MD17分子动力学数据集"""
    
    def __init__(self, root, dataset_arg=None, **kwargs):
        """
        Args:
            root: 数据根目录
            dataset_arg: 分子名称 (如 'aspirin', 'benzene')
        """

class ProteinRef(InMemoryDataset):
    """蛋白质参考数据集"""
    
    def __init__(self, root, dataset_arg=None, **kwargs):
        """
        Args:
            root: 数据根目录
            dataset_arg: 蛋白质名称 (如 'chignolin')
        """
```

### 3. 训练接口

#### Lightning模块
```python
class LNNP(LightningModule):
    """PyTorch Lightning训练模块"""
    
    def __init__(self, hparams, prior_model=None, mean=None, std=None):
        """
        Args:
            hparams: 超参数字典
            prior_model: 先验模型
            mean: 数据均值
            std: 数据标准差
        """
    
    def training_step(self, batch, batch_idx):
        """训练步骤"""
        
    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        
    def test_step(self, batch, batch_idx):
        """测试步骤"""
        
    def configure_optimizers(self):
        """配置优化器和学习率调度器"""
```

### 4. 输出模块

#### 标量输出
```python
class Scalar(OutputModel):
    """标量性质输出 (如能量)"""
    
    def pre_reduce(self, x, v, z, pos, batch):
        """原子级预测 -> 标量值"""

class EquivariantScalar(OutputModel):
    """等变标量输出"""
    
    def pre_reduce(self, x, v, z, pos, batch):
        """使用向量特征的等变标量预测"""
```

#### 向量输出
```python
class DipoleMoment(Scalar):
    """偶极矩预测"""
    
    def pre_reduce(self, x, v, z, pos, batch):
        """考虑质心的偶极矩计算"""
        
    def post_reduce(self, x):
        """返回偶极矩模长"""

class EquivariantVectorOutput(EquivariantScalar):
    """等变向量输出"""

    def pre_reduce(self, x, v, z, pos, batch):
        """直接返回向量特征"""

## 模型参数配置

### 1. 核心参数

#### 模型架构参数
```python
model_params = {
    # 基础架构
    'model': 'QuinNet',                    # 模型类型
    'embedding_dimension': 256,            # 嵌入维度
    'num_layers': 6,                       # 注意力层数
    'num_heads': 8,                        # 注意力头数

    # 径向基函数
    'num_rbf': 64,                         # 径向基函数数量
    'rbf_type': 'expnorm',                 # RBF类型 ['expnorm', 'gaussian']
    'trainable_rbf': False,                # RBF是否可训练

    # 截断和邻居
    'cutoff_lower': 0.0,                   # 下截断距离
    'cutoff_upper': 5.0,                   # 上截断距离
    'max_num_neighbors': 32,               # 最大邻居数

    # 激活函数
    'activation': 'silu',                  # 主激活函数
    'attn_activation': 'silu',             # 注意力激活函数

    # 其他
    'neighbor_embedding': True,            # 是否使用邻居嵌入
    'distance_influence': 'both',          # 距离影响 ['keys', 'values', 'both', 'none']
    'max_z': 100,                          # 最大原子序数
}
```

#### 训练参数
```python
training_params = {
    # 优化器
    'lr': 1e-4,                           # 学习率
    'weight_decay': 0.0,                  # 权重衰减
    'lr_warmup_steps': 1000,              # 学习率预热步数
    'lr_patience': 30,                    # 学习率调度耐心
    'lr_factor': 0.8,                     # 学习率衰减因子
    'lr_min': 1e-7,                       # 最小学习率

    # 训练设置
    'num_epochs': 1000,                   # 训练轮数
    'batch_size': 32,                     # 批次大小
    'early_stopping_patience': 600,       # 早停耐心

    # 损失权重
    'energy_weight': 0.01,                # 能量损失权重
    'force_weight': 0.99,                 # 力损失权重

    # 指数移动平均
    'ema_alpha_y': 0.05,                  # 能量EMA系数
    'ema_alpha_dy': 1.0,                  # 力EMA系数
}
```

#### 数据参数
```python
data_params = {
    # 数据集
    'dataset': 'MD17',                    # 数据集名称
    'dataset_arg': 'aspirin',             # 数据集参数
    'dataset_root': './data/',            # 数据根目录

    # 数据分割
    'train_size': 950,                    # 训练集大小
    'val_size': 50,                       # 验证集大小
    'test_size': None,                    # 测试集大小 (None=剩余全部)

    # 预处理
    'derivative': True,                   # 是否计算导数
    'standardize': True,                  # 是否标准化
    'reduce_op': 'add',                   # 聚合操作
}
```

### 2. 输出模块配置

#### 可用输出模块
```python
output_modules = {
    'Scalar': '标量输出 (能量等)',
    'EquivariantScalar': '等变标量输出',
    'DipoleMoment': '偶极矩',
    'EquivariantDipoleMoment': '等变偶极矩',
    'ElectronicSpatialExtent': '电子空间范围',
    'EquivariantVectorOutput': '等变向量输出',
}
```

#### 先验模型
```python
prior_models = {
    'Atomref': '原子参考能量',
    # 可扩展其他先验模型
}
```

## 使用示例

### 1. 基础训练示例

#### 命令行训练
```bash
# 使用配置文件训练
python train.py --conf example/hparams_aspirin.yaml

# 直接指定参数训练
python train.py \
    --dataset MD17 \
    --dataset-arg aspirin \
    --model QuinNet \
    --embedding-dimension 256 \
    --num-layers 6 \
    --batch-size 4 \
    --lr 0.0004 \
    --derivative true \
    --energy-weight 0.01 \
    --force-weight 0.99
```

#### Python API训练
```python
import torch
from src.module import LNNP
from src.data import DataModule

# 配置参数
args = {
    'dataset': 'MD17',
    'dataset_arg': 'aspirin',
    'model': 'QuinNet',
    'embedding_dimension': 256,
    'num_layers': 6,
    'derivative': True,
    # ... 其他参数
}

# 创建数据模块
data = DataModule(args)
data.prepare_data()

# 创建模型
model = LNNP(args, mean=data.mean, std=data.std)

# 训练 (使用PyTorch Lightning)
trainer = pl.Trainer(max_epochs=1000)
trainer.fit(model, datamodule=data)
```

### 2. 推理示例

#### 单分子预测
```python
import torch
from src.models.model import load_model

# 加载训练好的模型
model = load_model('path/to/checkpoint.ckpt')
model.eval()

# 准备输入数据
z = torch.tensor([6, 1, 1, 1, 1])  # 甲烷分子
pos = torch.tensor([
    [0.0, 0.0, 0.0],    # C
    [1.0, 0.0, 0.0],    # H
    [0.0, 1.0, 0.0],    # H
    [0.0, 0.0, 1.0],    # H
    [-1.0, 0.0, 0.0],   # H
])

# 预测
with torch.no_grad():
    energy, forces = model(z, pos)

print(f"预测能量: {energy.item():.6f} eV")
print(f"预测力:\n{forces}")
```

#### 批量预测
```python
# 批量数据
batch_z = torch.cat([z1, z2, z3])  # 拼接原子类型
batch_pos = torch.cat([pos1, pos2, pos3])  # 拼接坐标
batch_idx = torch.tensor([0, 0, 0, 0, 0,   # 第一个分子
                          1, 1, 1,         # 第二个分子
                          2, 2, 2, 2])     # 第三个分子

# 批量预测
energies, forces = model(batch_z, batch_pos, batch=batch_idx)
print(f"批量能量: {energies}")
```

### 3. 自定义数据集

#### 创建自定义数据集
```python
from torch_geometric.data import InMemoryDataset, Data
import numpy as np

class CustomDataset(InMemoryDataset):
    def __init__(self, root, transform=None):
        super().__init__(root, transform)
        self.data, self.slices = torch.load(self.processed_paths[0])

    @property
    def processed_file_names(self):
        return ['data.pt']

    def process(self):
        # 加载你的数据
        data_list = []

        for i in range(len(your_data)):
            # 创建Data对象
            data = Data(
                z=torch.tensor(atomic_numbers[i]),
                pos=torch.tensor(coordinates[i]),
                y=torch.tensor([[energies[i]]]),
                dy=torch.tensor(forces[i])
            )
            data_list.append(data)

        # 保存处理后的数据
        torch.save(self.collate(data_list), self.processed_paths[0])
```

## 性能优化

### 1. 内存优化
- **梯度检查点**: 在长序列上使用梯度检查点
- **混合精度**: 使用16位浮点数训练
- **批次大小调整**: 根据GPU内存调整批次大小

### 2. 计算优化
- **邻居列表缓存**: 缓存邻居列表减少重复计算
- **JIT编译**: 使用TorchScript加速推理
- **多GPU训练**: 使用DDP进行分布式训练

### 3. 数据优化
- **数据预加载**: 使用多进程数据加载
- **内存映射**: 对大数据集使用内存映射
- **数据增强**: 随机旋转和平移增强

## 故障排除

### 1. 常见错误

#### CUDA内存不足
```bash
# 解决方案
--batch-size 1          # 减小批次大小
--embedding-dimension 128  # 减小嵌入维度
--num-layers 3          # 减少层数
```

#### 收敛问题
```bash
# 解决方案
--lr-warmup-steps 1000  # 增加预热步数
--lr 1e-5               # 降低学习率
--weight-decay 1e-6     # 添加权重衰减
```

#### 数据加载错误
```bash
# 检查数据路径和格式
--dataset-root ./data/
--dataset-arg aspirin   # 确保数据集参数正确
```

### 2. 调试技巧

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 检查梯度
```python
# 检查梯度爆炸/消失
for name, param in model.named_parameters():
    if param.grad is not None:
        print(f"{name}: {param.grad.norm()}")
```

#### 可视化注意力权重
```python
# 在forward中保存注意力权重
attention_weights = []
# 然后可视化
import matplotlib.pyplot as plt
plt.imshow(attention_weights[0].detach().cpu())
```

## 扩展开发

### 1. 添加新的输出模块
```python
class CustomOutput(OutputModel):
    def __init__(self, hidden_channels, **kwargs):
        super().__init__(allow_prior_model=True)
        # 自定义初始化

    def pre_reduce(self, x, v, z, pos, batch):
        # 自定义预处理
        return custom_output
```

### 2. 自定义损失函数
```python
def custom_loss(pred, target, **kwargs):
    # 实现自定义损失
    return loss
```

### 3. 添加新的先验模型
```python
class CustomPrior(nn.Module):
    def forward(self, x, z, pos, batch):
        # 实现先验修正
        return corrected_x
```

## 引用信息

```bibtex
@inproceedings{wang2023quinnet,
  title={QuinNet: Efficiently Incorporating Quintuple Interactions into Geometric Deep Learning Force Fields},
  author={Wang, Zun and others},
  booktitle={NeurIPS},
  year={2023}
}
```
```
