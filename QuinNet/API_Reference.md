# QuinNet API 参考文档

## 核心模型类

### 1. QuinNet 主模型

```python
class QuinNet(nn.Module):
    """QuinNet完整模型，结合表示学习和输出模块"""
    
    def __init__(self,
                 representation_model: nn.Module,
                 output_model: nn.Module,
                 prior_model: Optional[nn.Module] = None,
                 reduce_op: str = 'add',
                 mean: Optional[Tensor] = None,
                 std: Optional[Tensor] = None,
                 derivative: bool = False):
        """
        Args:
            representation_model: 表示学习模型 (QuinNet_Module)
            output_model: 输出模块 (Scalar, DipoleMoment等)
            prior_model: 先验模型 (可选)
            reduce_op: 原子级预测的聚合方式 ['add', 'mean']
            mean: 训练数据均值，用于反标准化
            std: 训练数据标准差，用于反标准化
            derivative: 是否计算坐标导数 (力)
        """
    
    def forward(self,
                z: Tensor,                    # [N] 原子序数
                pos: Tensor,                  # [N, 3] 原子坐标
                batch: Optional[Tensor] = None, # [N] 批次索引
                q: Optional[Tensor] = None,   # [batch_size] 总电荷
                s: Optional[Tensor] = None    # [batch_size] 自旋多重度
                ) -> Tuple[Tensor, Optional[Tensor]]:
        """
        前向传播
        
        Returns:
            energy: [batch_size, 1] 预测的分子性质
            forces: [N, 3] 预测的原子力 (如果derivative=True)
        """
```

### 2. QuinNet_Module 表示学习模型

```python
class QuinNet_Module(nn.Module):
    """QuinNet核心的等变Transformer架构"""
    
    def __init__(self,
                 hidden_channels: int = 128,
                 num_layers: int = 6,
                 num_rbf: int = 50,
                 rbf_type: str = 'expnorm',
                 trainable_rbf: bool = True,
                 activation: str = 'silu',
                 attn_activation: str = 'silu',
                 neighbor_embedding: bool = True,
                 num_heads: int = 8,
                 distance_influence: str = 'both',
                 cutoff_lower: float = 0.0,
                 cutoff_upper: float = 5.0,
                 max_z: int = 100,
                 max_num_neighbors: int = 32):
        """
        Args:
            hidden_channels: 隐藏层维度
            num_layers: 注意力层数量
            num_rbf: 径向基函数数量
            rbf_type: 径向基函数类型 ['expnorm', 'gaussian', 'bessel']
            trainable_rbf: RBF参数是否可训练
            activation: 主激活函数 ['silu', 'relu', 'gelu']
            attn_activation: 注意力机制中的激活函数
            neighbor_embedding: 是否使用邻居嵌入
            num_heads: 多头注意力的头数
            distance_influence: 距离信息的使用方式 ['keys', 'values', 'both', 'none']
            cutoff_lower: 下截断距离 (Å)
            cutoff_upper: 上截断距离 (Å)
            max_z: 支持的最大原子序数
            max_num_neighbors: 每个原子的最大邻居数
        """
    
    def forward(self,
                z: Tensor,                    # [N] 原子序数
                pos: Tensor,                  # [N, 3] 原子坐标
                batch: Tensor,                # [N] 批次索引
                q: Optional[Tensor] = None,   # [batch_size] 电荷
                s: Optional[Tensor] = None    # [batch_size] 自旋
                ) -> Tuple[Tensor, Tensor, Tensor, Tensor, Tensor]:
        """
        Returns:
            x: [N, hidden_channels] 标量特征
            vec: [N, 8, hidden_channels] 向量特征
            z: [N] 原子序数 (传递)
            pos: [N, 3] 原子坐标 (传递)
            batch: [N] 批次索引 (传递)
        """
```

### 3. EquivariantMultiHeadAttention 注意力层

```python
class EquivariantMultiHeadAttention(MessagePassing):
    """实现五体相互作用的等变多头注意力机制"""
    
    def __init__(self,
                 hidden_channels: int,
                 distance_influence: str,
                 num_heads: int,
                 activation: nn.Module,
                 attn_activation: str,
                 cutoff_lower: float,
                 cutoff_upper: float,
                 last_layer: bool = False):
        """
        Args:
            hidden_channels: 隐藏层维度
            distance_influence: 距离影响方式
            num_heads: 注意力头数
            activation: 激活函数类
            attn_activation: 注意力激活函数名
            cutoff_lower: 下截断距离
            cutoff_upper: 上截断距离
            last_layer: 是否为最后一层
        """
    
    def forward(self,
                x: Tensor,        # [N, hidden_channels] 标量特征
                vec: Tensor,      # [N, 8, hidden_channels] 向量特征
                edge_index: Tensor, # [2, E] 边索引
                r_ij: Tensor,     # [E] 边距离
                f_ij: Tensor,     # [E, hidden_channels] 边特征
                d_ij: Tensor      # [E, 8, 3] 边向量
                ) -> Tuple[Tensor, Tensor, Optional[Tensor]]:
        """
        Returns:
            dx: [N, hidden_channels] 标量特征更新
            dvec: [N, 8, hidden_channels] 向量特征更新
            edge_update: [E, hidden_channels] 边特征更新 (非最后层)
        """
```

## 数据处理类

### 1. 数据集类

#### QM9 数据集
```python
class QM9(QM9_geometric):
    """QM9量子化学数据集"""
    
    def __init__(self,
                 root: str,
                 transform: Optional[Callable] = None,
                 dataset_arg: Optional[str] = None,
                 pre_transform: Optional[Callable] = None,
                 pre_filter: Optional[Callable] = None):
        """
        Args:
            root: 数据根目录
            dataset_arg: 目标性质名称，如 'mu', 'alpha', 'homo'等
            transform: 数据变换函数
            pre_transform: 预处理变换
            pre_filter: 数据过滤函数
        """
    
    def get_atomref(self, max_z: int = 100) -> Optional[Tensor]:
        """
        获取原子参考能量
        
        Args:
            max_z: 最大原子序数
            
        Returns:
            atomref: [max_z, 1] 原子参考能量张量
        """
```

#### MD17 数据集
```python
class MD17(InMemoryDataset):
    """MD17分子动力学数据集"""
    
    available_molecules = [
        'aspirin', 'benzene', 'ethanol', 'malonaldehyde',
        'naphthalene', 'salicylic_acid', 'toluene', 'uracil'
    ]
    
    def __init__(self,
                 root: str,
                 transform: Optional[Callable] = None,
                 dataset_arg: Optional[str] = None):
        """
        Args:
            root: 数据根目录
            dataset_arg: 分子名称，必须在available_molecules中
        """
    
    def get_atomref(self, max_z: int = 100) -> Optional[Tensor]:
        """返回None，MD17不使用原子参考能量"""
```

#### ProteinRef 数据集
```python
class ProteinRef(InMemoryDataset):
    """蛋白质参考数据集"""
    
    protein_files = {'chignolin': 'chignolin_dft.npz'}
    
    self_energies = {
        1: -0.496665677271,    # H
        6: -37.8289474402,     # C
        7: -54.5677547104,     # N
        8: -75.0321126521,     # O
        16: -398.063946327,    # S
    }
    
    def __init__(self,
                 root: str,
                 transform: Optional[Callable] = None,
                 dataset_arg: Optional[str] = None):
        """
        Args:
            root: 数据根目录
            dataset_arg: 蛋白质名称，如 'chignolin'
        """
```

### 2. 数据模块

```python
class DataModule:
    """PyTorch Lightning数据模块"""
    
    def __init__(self, args: dict):
        """
        Args:
            args: 包含数据配置的字典
                - dataset: 数据集名称
                - dataset_root: 数据根目录
                - dataset_arg: 数据集参数
                - batch_size: 批次大小
                - train_size: 训练集大小
                - val_size: 验证集大小
                - test_size: 测试集大小
                - num_workers: 数据加载进程数
        """
    
    def prepare_data(self) -> None:
        """下载和预处理数据"""
    
    def setup(self, stage: Optional[str] = None) -> None:
        """设置数据集分割"""
    
    def train_dataloader(self) -> DataLoader:
        """返回训练数据加载器"""
    
    def val_dataloader(self) -> DataLoader:
        """返回验证数据加载器"""
    
    def test_dataloader(self) -> DataLoader:
        """返回测试数据加载器"""
    
    @property
    def mean(self) -> Optional[Tensor]:
        """训练数据均值"""
    
    @property
    def std(self) -> Optional[Tensor]:
        """训练数据标准差"""
```

## 输出模块

### 1. 基础输出模块

```python
class OutputModel(nn.Module, metaclass=ABCMeta):
    """输出模块基类"""
    
    def __init__(self, allow_prior_model: bool):
        """
        Args:
            allow_prior_model: 是否允许使用先验模型
        """
    
    @abstractmethod
    def pre_reduce(self,
                   x: Tensor,     # [N, hidden_channels] 标量特征
                   v: Tensor,     # [N, vec_dim, hidden_channels] 向量特征
                   z: Tensor,     # [N] 原子序数
                   pos: Tensor,   # [N, 3] 原子坐标
                   batch: Tensor  # [N] 批次索引
                   ) -> Tensor:
        """原子级预测，在聚合前调用"""
    
    def post_reduce(self, x: Tensor) -> Tensor:
        """分子级后处理，在聚合后调用"""
        return x
```

### 2. 具体输出模块

#### 标量输出
```python
class Scalar(OutputModel):
    """标量性质输出 (如能量)"""
    
    def __init__(self,
                 hidden_channels: int,
                 activation: str = "silu",
                 allow_prior_model: bool = True):
        """
        Args:
            hidden_channels: 输入特征维度
            activation: 激活函数名称
            allow_prior_model: 是否允许先验模型
        """

class EquivariantScalar(OutputModel):
    """等变标量输出"""
    
    def __init__(self,
                 hidden_channels: int,
                 activation: str = "silu",
                 allow_prior_model: bool = True):
        """使用向量特征的等变标量预测"""
```

#### 向量输出
```python
class DipoleMoment(Scalar):
    """偶极矩预测"""
    
    def pre_reduce(self, x, v, z, pos, batch) -> Tensor:
        """
        计算偶极矩，考虑质心位置
        
        Returns:
            dipole_contributions: [N, 3] 每个原子的偶极矩贡献
        """
    
    def post_reduce(self, x: Tensor) -> Tensor:
        """
        Args:
            x: [batch_size, 3] 分子偶极矩向量
            
        Returns:
            dipole_magnitude: [batch_size, 1] 偶极矩模长
        """

class EquivariantVectorOutput(EquivariantScalar):
    """等变向量输出"""
    
    def pre_reduce(self, x, v, z, pos, batch) -> Tensor:
        """
        直接返回向量特征
        
        Returns:
            vector_output: [N, 3] 向量输出
        """
```

## 训练模块

### 1. Lightning模块

```python
class LNNP(LightningModule):
    """神经网络势能面Lightning训练模块"""
    
    def __init__(self,
                 hparams: dict,
                 prior_model: Optional[nn.Module] = None,
                 mean: Optional[Tensor] = None,
                 std: Optional[Tensor] = None):
        """
        Args:
            hparams: 超参数字典
            prior_model: 先验模型
            mean: 训练数据均值
            std: 训练数据标准差
        """
    
    def training_step(self, batch, batch_idx) -> Tensor:
        """
        训练步骤
        
        Args:
            batch: 批次数据
            batch_idx: 批次索引
            
        Returns:
            loss: 训练损失
        """
    
    def validation_step(self, batch, batch_idx, *args) -> Tensor:
        """
        验证步骤
        
        Returns:
            loss: 验证损失
        """
    
    def test_step(self, batch, batch_idx) -> Tensor:
        """
        测试步骤
        
        Returns:
            loss: 测试损失
        """
    
    def configure_optimizers(self):
        """
        配置优化器和学习率调度器
        
        Returns:
            optimizer: AdamW优化器
            scheduler: ReduceLROnPlateau调度器
        """
```

## 工具函数

### 1. 模型创建和加载

```python
def create_model(args: dict,
                 prior_model: Optional[nn.Module] = None,
                 mean: Optional[Tensor] = None,
                 std: Optional[Tensor] = None) -> QuinNet:
    """
    根据配置创建QuinNet模型
    
    Args:
        args: 模型配置字典
        prior_model: 先验模型
        mean: 数据均值
        std: 数据标准差
        
    Returns:
        model: 完整的QuinNet模型
    """

def load_model(filepath: str,
               args: Optional[dict] = None,
               device: str = 'cpu',
               **kwargs) -> QuinNet:
    """
    从检查点加载模型
    
    Args:
        filepath: 检查点文件路径
        args: 模型参数 (可选，从检查点读取)
        device: 目标设备
        **kwargs: 额外的模型参数覆盖
        
    Returns:
        model: 加载的模型
    """
```

### 2. 几何处理工具

```python
class Distance(nn.Module):
    """距离计算和邻居查找"""
    
    def __init__(self,
                 cutoff_lower: float,
                 cutoff_upper: float,
                 max_num_neighbors: int = 32,
                 return_vecs: bool = False,
                 loop: bool = True):
        """
        Args:
            cutoff_lower: 下截断距离
            cutoff_upper: 上截断距离
            max_num_neighbors: 最大邻居数
            return_vecs: 是否返回方向向量
            loop: 是否包含自环
        """
    
    def forward(self,
                pos: Tensor,      # [N, 3] 原子坐标
                batch: Tensor     # [N] 批次索引
                ) -> Tuple[Tensor, Tensor, Optional[Tensor]]:
        """
        Returns:
            edge_index: [2, E] 边索引
            edge_weight: [E] 边距离
            edge_vec: [E, 3] 边向量 (如果return_vecs=True)
        """

class Sphere(nn.Module):
    """球谐函数展开"""
    
    def __init__(self, l: int = 2):
        """
        Args:
            l: 球谐函数最大阶数
        """
    
    def forward(self, edge_vec: Tensor) -> Tensor:
        """
        Args:
            edge_vec: [E, 3] 归一化边向量
            
        Returns:
            spherical_harmonics: [E, (l+1)^2] 球谐函数特征
        """
```

### 3. 径向基函数

```python
class GaussianSmearing(nn.Module):
    """高斯径向基函数"""
    
    def __init__(self,
                 cutoff_lower: float = 0.0,
                 cutoff_upper: float = 5.0,
                 num_rbf: int = 50,
                 trainable: bool = True):
        """
        Args:
            cutoff_lower: 下截断距离
            cutoff_upper: 上截断距离
            num_rbf: 径向基函数数量
            trainable: 参数是否可训练
        """

class ExpNormalSmearing(nn.Module):
    """指数-正态径向基函数"""
    
    def __init__(self,
                 cutoff_lower: float = 0.0,
                 cutoff_upper: float = 5.0,
                 num_rbf: int = 50,
                 trainable: bool = True):
        """QuinNet默认使用的RBF"""
```

## 常量和映射

### 1. 激活函数映射
```python
act_class_mapping = {
    'silu': nn.SiLU,
    'swish': nn.SiLU,
    'relu': nn.ReLU,
    'gelu': nn.GELU,
    'tanh': nn.Tanh,
    'sigmoid': nn.Sigmoid,
}
```

### 2. 径向基函数映射
```python
rbf_class_mapping = {
    'gaussian': GaussianSmearing,
    'expnorm': ExpNormalSmearing,
    'bessel': BesselBasisLayer,
}
```

### 3. 单位转换常量
```python
# 能量单位转换
HAR2EV = 27.211386246      # Hartree to eV
KCALMOL2EV = 0.04336414    # kcal/mol to eV

# 长度单位转换  
Bohr = 0.529177210903      # Bohr to Angstrom
Hartree = 27.211386246     # Hartree to eV
```
