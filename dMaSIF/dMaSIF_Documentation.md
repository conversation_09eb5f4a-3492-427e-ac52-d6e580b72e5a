# dMaSIF - Fast End-to-End Learning on Protein Surfaces

## 项目概述

dMaSIF (differentiable Molecular Surface Interaction Fingerprinting) 是一个用于蛋白质表面深度学习的端到端框架。该项目实现了基于几何深度学习的蛋白质功能位点识别和蛋白质-蛋白质相互作用预测。

### 核心特性
- **端到端学习**: 直接从原子坐标和化学类型学习，无需预计算特征
- **动态表面生成**: 从原子点云实时计算分子表面
- **高效几何卷积**: 新颖的准测地线卷积层
- **多任务支持**: 支持相互作用位点识别和蛋白质配对预测

## 系统架构

### 1. 模型架构 (model.py)

#### 主要类结构

```python
class dMaSIF(nn.Module):
    """主要的dMaSIF模型类"""
    
    def __init__(self, args):
        # 几何特征计算尺度
        self.curvature_scales = args.curvature_scales  # [1.0, 2.0, 3.0, 5.0, 10.0]
        
        # 化学特征提取网络
        self.atomnet = AtomNet_MP(args)
        
        # 嵌入层选择
        if args.embedding_layer == "dMaSIF":
            self.conv = dMaSIFConv_seg(args, ...)
        elif args.embedding_layer == "DGCNN":
            self.conv = DGCNN_seg(...)
        elif args.embedding_layer == "PointNet++":
            self.conv = PointNet2_seg(...)
```

#### 核心方法

**1. features(P, i=1)**
- **功能**: 从蛋白质表面或原子云估计几何和化学特征
- **输入**: 
  - `P`: 蛋白质数据字典，包含原子坐标、类型等
  - `i`: 蛋白质索引
- **输出**: 拼接的几何和化学特征张量 `[N, feature_dim]`
- **处理流程**:
  1. 如果未使用预计算网格，从原子生成伪表面
  2. 计算多尺度曲率特征
  3. 通过AtomNet计算化学特征
  4. 拼接几何和化学特征

**2. embed(P)**
- **功能**: 将蛋白质所有点嵌入到高维向量空间
- **输入**: 蛋白质数据字典 `P`
- **输出**: 卷积时间和内存使用量
- **处理流程**:
  1. 提取输入特征
  2. 根据嵌入层类型进行卷积
  3. 返回性能指标

**3. forward(P1, P2=None)**
- **功能**: 模型前向传播
- **输入**: 
  - `P1`: 第一个蛋白质数据
  - `P2`: 第二个蛋白质数据（可选，用于配对任务）
- **输出**: 包含预测结果和性能指标的字典

### 2. 原子特征提取

#### AtomNet_MP 类
```python
class AtomNet_MP(nn.Module):
    """基于消息传递的原子特征提取网络"""
    
    def __init__(self, args):
        self.transform_types = nn.Sequential(...)  # 原子类型变换
        self.embed = Atom_embedding_MP(args)       # 原子嵌入
        self.atom_atom = Atom_Atom_embedding_MP(args)  # 原子间交互
```

#### 特征提取流程
1. **原子类型变换**: 将原子类型编码转换为可学习表示
2. **原子间消息传递**: 通过k近邻图进行原子间信息交换
3. **表面点特征聚合**: 将原子特征聚合到表面点

### 3. 几何处理 (geometry_processing.py)

#### 核心函数

**atoms_to_points_normals(atoms, batch, atomtypes, resolution=1.0, sup_sampling=20)**
- **功能**: 从原子坐标生成表面点云和法向量
- **参数**:
  - `atoms`: 原子坐标 `[N_atoms, 3]`
  - `batch`: 批次索引
  - `atomtypes`: 原子类型
  - `resolution`: 点云分辨率
  - `sup_sampling`: 原子周围超采样比例
- **输出**: 表面点坐标、法向量、批次索引

**curvatures(points, triangles=None, normals=None, scales=[1.0, 2.0, 3.0, 5.0, 10.0], batch=None)**
- **功能**: 计算多尺度曲率特征
- **参数**:
  - `points`: 表面点坐标
  - `triangles`: 三角网格（可选）
  - `normals`: 法向量
  - `scales`: 计算曲率的尺度列表
- **输出**: 多尺度平均曲率和高斯曲率 `[N_points, 2*len(scales)]`

## 数据格式

### 1. 输入数据格式

#### 蛋白质数据字典 (P)
```python
P = {
    # 原子信息
    "atoms": torch.Tensor,           # [N_atoms, 3] 原子坐标
    "atomtypes": torch.Tensor,       # [N_atoms, 6] 原子类型one-hot编码
    "batch_atoms": torch.Tensor,     # [N_atoms] 原子批次索引
    
    # 表面信息（可选，如果use_mesh=True）
    "xyz": torch.Tensor,             # [N_points, 3] 表面点坐标
    "normals": torch.Tensor,         # [N_points, 3] 表面法向量
    "triangles": torch.Tensor,       # [N_triangles, 3] 三角网格连接
    "batch": torch.Tensor,           # [N_points] 表面点批次索引
    
    # 标签信息（训练时）
    "labels": torch.Tensor,          # [N_points] 相互作用位点标签
    "mesh_labels": torch.Tensor,     # [N_mesh_points] 网格标签
}
```

#### 原子类型编码
```python
ele2num = {"C": 0, "H": 1, "O": 2, "N": 3, "S": 4, "SE": 5}
# 转换为6维one-hot向量
```

### 2. 数据集结构

#### 训练/测试列表
- `lists/training.txt`: 训练集蛋白质ID列表
- `lists/testing.txt`: 测试集蛋白质ID列表  
- `lists/training_ppi.txt`: 蛋白质配对训练集
- `lists/testing_ppi.txt`: 蛋白质配对测试集

#### 数据目录结构
```
surface_data/
├── raw/                    # 原始数据
│   ├── 01-benchmark_pdbs/  # PDB文件
│   └── 01-benchmark_surfaces/ # PLY表面文件
└── processed/              # 处理后的数据
    ├── training_pairs_data.pt
    ├── testing_pairs_data.pt
    └── *_data_ids.npy
```

## 函数接口详解

### 1. 训练接口 (main_training.py)

#### 主要参数
```python
# 实验配置
--experiment_name: str          # 实验名称
--embedding_layer: str          # 嵌入层类型 ["dMaSIF", "DGCNN", "PointNet++"]
--site: bool                    # 是否进行位点预测
--search: bool                  # 是否进行配对搜索

# 几何参数
--resolution: float = 1.0       # 点云分辨率
--distance: float = 1.05        # 表面生成距离参数
--variance: float = 0.1         # 表面生成方差参数
--curvature_scales: list        # 曲率计算尺度

# 网络参数
--in_channels: int = 16         # 嵌入维度
--n_layers: int = 1             # 卷积层数
--radius: float = 9.0           # 卷积半径
--dropout: float = 0.0          # Dropout比例

# 训练参数
--n_epochs: int = 50            # 训练轮数
--batch_size: int = 1           # 批次大小
--device: str = "cuda:0"        # 设备
--random_rotation: bool         # 是否随机旋转
```

#### 使用示例
```bash
# 位点预测训练
python main_training.py \
    --experiment_name dMaSIF_site_1layer_15A \
    --embedding_layer dMaSIF \
    --site True \
    --single_protein True \
    --radius 15.0 \
    --n_layers 1

# 配对搜索训练  
python main_training.py \
    --experiment_name dMaSIF_search_1layer_12A \
    --embedding_layer dMaSIF \
    --search True \
    --radius 12.0 \
    --n_layers 1
```

### 2. 推理接口 (main_inference.py)

#### 主要参数
```python
--experiment_name: str          # 模型名称
--single_pdb: str              # 单个PDB推理
--pdb_list: str                # PDB列表文件
```

#### 使用示例
```bash
# 单个蛋白质推理
python main_inference.py \
    --experiment_name dMaSIF_site_1layer_15A_epoch25 \
    --single_pdb 1A0G_B

# 批量推理
python main_inference.py \
    --experiment_name dMaSIF_site_1layer_15A_epoch25 \
    --pdb_list lists/testing.txt
```

### 3. 数据预处理接口

#### PDB转换 (convert_pdb2npy.py)
```python
def load_structure_np(fname, center):
    """从PDB文件加载原子结构

    Args:
        fname: PDB文件路径
        center: 是否中心化坐标

    Returns:
        dict: {"xyz": 原子坐标, "types": 原子类型one-hot}
    """

def convert_pdbs(pdb_dir, npy_dir):
    """批量转换PDB文件为numpy格式"""
```

#### PLY转换 (convert_ply2npy.py)
```python
def load_mesh(fname, center):
    """从PLY文件加载网格表面

    Returns:
        dict: {"xyz": 顶点坐标, "faces": 面片索引, "normals": 法向量}
    """
```

## 模型输出格式

### 1. 位点预测输出
```python
output = {
    "P1": {
        "xyz": torch.Tensor,              # [N, 3] 表面点坐标
        "embedding_1": torch.Tensor,      # [N, emb_dim] 点嵌入
        "iface_preds": torch.Tensor,      # [N, 1] 位点预测概率
        "labels": torch.Tensor,           # [N, 1] 真实标签（训练时）
    },
    "R_values": dict,                     # 表示维度信息
    "conv_time": float,                   # 卷积计算时间
    "memory_usage": int,                  # 内存使用量
}
```

### 2. 配对搜索输出
```python
output = {
    "P1": {
        "embedding_1": torch.Tensor,      # [N1, emb_dim] 蛋白质1嵌入
        "embedding_2": torch.Tensor,      # [N1, emb_dim] 蛋白质1第二嵌入
    },
    "P2": {
        "embedding_1": torch.Tensor,      # [N2, emb_dim] 蛋白质2嵌入
        "embedding_2": torch.Tensor,      # [N2, emb_dim] 蛋白质2第二嵌入
    },
    "R_values": dict,
    "conv_time": float,
    "memory_usage": int,
}
```

## 核心算法详解

### 1. 表面生成算法

#### atoms_to_points_normals 实现原理
1. **原子周围采样**: 在每个原子周围按分辨率采样点
2. **表面点筛选**: 使用距离阈值筛选表面点
3. **法向量估计**: 基于局部几何估计法向量
4. **去重和优化**: 移除重复点并优化分布

```python
def atoms_to_points_normals(atoms, batch_atoms, atomtypes,
                           resolution=1.0, sup_sampling=20, distance=1.05):
    """
    核心表面生成函数

    算法步骤:
    1. 对每个原子周围进行超采样
    2. 计算原子间距离，筛选表面点
    3. 估计表面法向量
    4. 应用网格聚类去重
    """
```

### 2. 曲率计算算法

#### 多尺度曲率特征
```python
def curvatures(points, normals, scales, batch):
    """
    计算多尺度曲率特征

    对每个尺度s:
    1. 构建半径为s的邻域
    2. 拟合局部二次曲面
    3. 计算平均曲率和高斯曲率
    4. 拼接所有尺度的特征
    """
```

### 3. 准测地线卷积

#### dMaSIFConv 核心思想
1. **切向量计算**: 在表面每点计算切向量基
2. **方向权重**: 学习方向相关的权重
3. **邻域聚合**: 在测地线邻域内聚合特征
4. **旋转不变性**: 保持几何变换不变性

## 性能优化

### 1. 内存优化
- **LazyTensor**: 使用KeOps库的LazyTensor减少内存占用
- **批处理**: 高效的批处理机制
- **动态计算**: 避免存储大型中间结果

### 2. 计算优化
- **GPU加速**: 全面的CUDA支持
- **并行计算**: 多尺度特征并行计算
- **缓存机制**: 重复计算的缓存

## 实验配置

### 1. 硬件要求
- **GPU**: NVIDIA RTX 2080 Ti 或 Tesla V100
- **内存**: 至少16GB RAM
- **存储**: 至少50GB可用空间

### 2. 软件环境
```bash
# 核心依赖
torch==1.6.0
torch-geometric==1.6.1
pykeops==1.4.1
biopython==1.78
plyfile==0.7.2

# 安装命令
pip install -r requirements.txt
```

### 3. 数据准备
```bash
# 下载数据集
python data_preprocessing/download_pdb.py

# 转换PDB文件
python data_preprocessing/convert_pdb2npy.py

# 转换PLY文件
python data_preprocessing/convert_ply2npy.py
```

## 基准测试结果

### 1. 位点预测性能
- **数据集**: 3004个训练蛋白质，654个测试蛋白质
- **评估指标**: ROC-AUC, PR-AUC
- **最佳结果**: ROC-AUC > 0.85

### 2. 配对搜索性能
- **数据集**: 蛋白质-蛋白质相互作用对
- **评估指标**: 匹配准确率
- **计算效率**: 比传统方法快10-100倍

### 3. 运行时间对比
```
方法          | 训练时间/epoch | 推理时间/蛋白质
dMaSIF       | 2-3小时        | 0.1-0.5秒
传统方法      | 10-20小时      | 5-30秒
```

## 故障排除

### 1. 常见错误
- **CUDA内存不足**: 减小batch_size或radius
- **依赖版本冲突**: 严格按照requirements.txt安装
- **数据格式错误**: 检查PDB文件格式和原子类型

### 2. 调试技巧
- **使用profile模式**: `--profile True`
- **检查中间输出**: 保存中间特征进行分析
- **可视化结果**: 使用VTK格式输出进行可视化

## 扩展开发

### 1. 添加新的嵌入层
```python
class CustomConv(nn.Module):
    def __init__(self, args):
        # 实现自定义卷积层
        pass

    def forward(self, xyz, features, batch):
        # 前向传播逻辑
        return embeddings
```

### 2. 自定义损失函数
```python
def custom_loss(predictions, targets, weights=None):
    # 实现自定义损失
    return loss
```

### 3. 新任务适配
- 修改输出层维度
- 调整损失函数
- 更新评估指标

## 引用信息

```bibtex
@article{sverrisson2020fast,
  title={Fast end-to-end learning on protein surfaces},
  author={Sverrisson, Freyr and Feydy, Jean and Correia, Bruno E and Bronstein, Michael M},
  journal={bioRxiv},
  year={2020}
}
```

## 许可证

本项目采用 Creative Commons Attribution-NonCommercial-NoDerivatives 4.0 International License 许可证。
