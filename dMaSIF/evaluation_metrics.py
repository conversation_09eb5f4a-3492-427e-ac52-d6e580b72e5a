#!/usr/bin/env python3
"""
dMaSIF 详细评估指标计算和可视化脚本

该脚本提供了完整的模型评估功能，包括：
1. 详细的分类指标计算
2. 混淆矩阵可视化
3. ROC和PR曲线绘制
4. 评估报告生成
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, matthews_corrcoef, roc_auc_score,
    average_precision_score, roc_curve, precision_recall_curve
)
import pandas as pd
from pathlib import Path
import json


class ModelEvaluator:
    """模型评估器类"""
    
    def __init__(self, save_dir="evaluation_results"):
        """
        初始化评估器
        
        Args:
            save_dir (str): 结果保存目录
        """
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(exist_ok=True)
        
    def compute_all_metrics(self, y_true, y_pred, y_prob=None, threshold=0.5):
        """
        计算所有评估指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签或概率
            y_prob: 预测概率（可选）
            threshold: 分类阈值
            
        Returns:
            dict: 包含所有指标的字典
        """
        # 确保输入是numpy数组
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        
        # 如果没有提供概率，使用预测值作为概率
        if y_prob is None:
            y_prob = y_pred.copy()
        else:
            y_prob = np.array(y_prob)
            
        # 如果预测值是概率，转换为二分类标签
        if y_pred.max() <= 1.0 and y_pred.min() >= 0.0:
            y_pred_binary = (y_pred > threshold).astype(int)
        else:
            y_pred_binary = y_pred.astype(int)
            
        y_true = y_true.astype(int)
        
        metrics = {}
        
        try:
            # 基础分类指标
            metrics['accuracy'] = accuracy_score(y_true, y_pred_binary)
            metrics['precision'] = precision_score(y_true, y_pred_binary, zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred_binary, zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred_binary, zero_division=0)
            
            # 混淆矩阵
            cm = confusion_matrix(y_true, y_pred_binary)
            if cm.shape == (2, 2):
                tn, fp, fn, tp = cm.ravel()
                metrics['true_negatives'] = int(tn)
                metrics['false_positives'] = int(fp)
                metrics['false_negatives'] = int(fn)
                metrics['true_positives'] = int(tp)
                
                # 特异性和敏感性
                metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0
                metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
                
                # 阳性预测值和阴性预测值
                metrics['ppv'] = tp / (tp + fp) if (tp + fp) > 0 else 0.0  # 等同于precision
                metrics['npv'] = tn / (tn + fn) if (tn + fn) > 0 else 0.0
                
            else:
                # 处理只有一个类别的情况
                metrics.update({
                    'true_negatives': 0, 'false_positives': 0,
                    'false_negatives': 0, 'true_positives': 0,
                    'specificity': 0.0, 'sensitivity': 0.0,
                    'ppv': 0.0, 'npv': 0.0
                })
            
            # 马修斯相关系数
            metrics['mcc'] = matthews_corrcoef(y_true, y_pred_binary)
            
            # ROC-AUC和PR-AUC（需要概率值）
            if len(np.unique(y_true)) > 1:
                metrics['roc_auc'] = roc_auc_score(y_true, y_prob)
                metrics['pr_auc'] = average_precision_score(y_true, y_prob)
            else:
                metrics['roc_auc'] = 0.0
                metrics['pr_auc'] = 0.0
                
        except Exception as e:
            print(f"Error computing metrics: {e}")
            # 返回默认值
            metrics = {
                'accuracy': 0.0, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0,
                'specificity': 0.0, 'sensitivity': 0.0, 'ppv': 0.0, 'npv': 0.0,
                'mcc': 0.0, 'roc_auc': 0.0, 'pr_auc': 0.0,
                'true_negatives': 0, 'false_positives': 0,
                'false_negatives': 0, 'true_positives': 0
            }
        
        return metrics
    
    def plot_confusion_matrix(self, y_true, y_pred, threshold=0.5, 
                            title="Confusion Matrix", save_name="confusion_matrix.png"):
        """
        绘制混淆矩阵热图
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签或概率
            threshold: 分类阈值
            title: 图表标题
            save_name: 保存文件名
        """
        # 转换为二分类标签
        y_true = np.array(y_true).astype(int)
        y_pred = np.array(y_pred)
        
        if y_pred.max() <= 1.0 and y_pred.min() >= 0.0:
            y_pred_binary = (y_pred > threshold).astype(int)
        else:
            y_pred_binary = y_pred.astype(int)
        
        # 计算混淆矩阵
        cm = confusion_matrix(y_true, y_pred_binary)
        
        # 创建图表
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Negative', 'Positive'],
                   yticklabels=['Negative', 'Positive'])
        plt.title(title)
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        
        # 添加统计信息
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()
            accuracy = (tp + tn) / (tp + tn + fp + fn)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            stats_text = f'Accuracy: {accuracy:.3f}\nPrecision: {precision:.3f}\nRecall: {recall:.3f}\nF1-Score: {f1:.3f}'
            plt.text(2.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
        
        plt.tight_layout()
        plt.savefig(self.save_dir / save_name, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_roc_curve(self, y_true, y_prob, title="ROC Curve", save_name="roc_curve.png"):
        """
        绘制ROC曲线
        
        Args:
            y_true: 真实标签
            y_prob: 预测概率
            title: 图表标题
            save_name: 保存文件名
        """
        y_true = np.array(y_true).astype(int)
        y_prob = np.array(y_prob)
        
        if len(np.unique(y_true)) < 2:
            print("Warning: Only one class present in y_true. ROC curve cannot be computed.")
            return
        
        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(y_true, y_prob)
        roc_auc = roc_auc_score(y_true, y_prob)
        
        # 绘制ROC曲线
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2, 
                label=f'ROC curve (AUC = {roc_auc:.3f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', 
                label='Random classifier')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(title)
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.save_dir / save_name, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_pr_curve(self, y_true, y_prob, title="Precision-Recall Curve", 
                     save_name="pr_curve.png"):
        """
        绘制Precision-Recall曲线
        
        Args:
            y_true: 真实标签
            y_prob: 预测概率
            title: 图表标题
            save_name: 保存文件名
        """
        y_true = np.array(y_true).astype(int)
        y_prob = np.array(y_prob)
        
        if len(np.unique(y_true)) < 2:
            print("Warning: Only one class present in y_true. PR curve cannot be computed.")
            return
        
        # 计算PR曲线
        precision, recall, thresholds = precision_recall_curve(y_true, y_prob)
        pr_auc = average_precision_score(y_true, y_prob)
        
        # 绘制PR曲线
        plt.figure(figsize=(8, 6))
        plt.plot(recall, precision, color='blue', lw=2,
                label=f'PR curve (AUC = {pr_auc:.3f})')
        
        # 基线（随机分类器）
        baseline = np.sum(y_true) / len(y_true)
        plt.axhline(y=baseline, color='red', linestyle='--', 
                   label=f'Random classifier (AP = {baseline:.3f})')
        
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title(title)
        plt.legend(loc="lower left")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.save_dir / save_name, dpi=300, bbox_inches='tight')
        plt.close()
        
    def generate_report(self, y_true, y_pred, y_prob=None, threshold=0.5,
                       model_name="dMaSIF", dataset_name="Test"):
        """
        生成完整的评估报告
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签或概率
            y_prob: 预测概率（可选）
            threshold: 分类阈值
            model_name: 模型名称
            dataset_name: 数据集名称
        """
        print(f"\n{'='*60}")
        print(f"模型评估报告 - {model_name} on {dataset_name}")
        print(f"{'='*60}")
        
        # 计算所有指标
        metrics = self.compute_all_metrics(y_true, y_pred, y_prob, threshold)
        
        # 打印基础指标
        print(f"\n基础分类指标:")
        print(f"准确率 (Accuracy):     {metrics['accuracy']:.4f}")
        print(f"精确率 (Precision):    {metrics['precision']:.4f}")
        print(f"召回率 (Recall):       {metrics['recall']:.4f}")
        print(f"F1分数 (F1-Score):     {metrics['f1_score']:.4f}")
        print(f"特异性 (Specificity):  {metrics['specificity']:.4f}")
        print(f"马修斯相关系数 (MCC):   {metrics['mcc']:.4f}")
        
        # 打印AUC指标
        print(f"\nAUC指标:")
        print(f"ROC-AUC:              {metrics['roc_auc']:.4f}")
        print(f"PR-AUC:               {metrics['pr_auc']:.4f}")
        
        # 打印混淆矩阵
        print(f"\n混淆矩阵:")
        print(f"真阴性 (TN): {metrics['true_negatives']:6d}  |  假阳性 (FP): {metrics['false_positives']:6d}")
        print(f"假阴性 (FN): {metrics['false_negatives']:6d}  |  真阳性 (TP): {metrics['true_positives']:6d}")
        
        # 生成可视化
        if y_prob is None:
            y_prob = y_pred
            
        self.plot_confusion_matrix(y_true, y_pred, threshold, 
                                 f"{model_name} - Confusion Matrix",
                                 f"{model_name}_{dataset_name}_confusion_matrix.png")
        
        self.plot_roc_curve(y_true, y_prob,
                           f"{model_name} - ROC Curve", 
                           f"{model_name}_{dataset_name}_roc_curve.png")
        
        self.plot_pr_curve(y_true, y_prob,
                          f"{model_name} - Precision-Recall Curve",
                          f"{model_name}_{dataset_name}_pr_curve.png")
        
        # 保存指标到JSON文件
        metrics_file = self.save_dir / f"{model_name}_{dataset_name}_metrics.json"
        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=2)
        
        print(f"\n评估结果已保存到: {self.save_dir}")
        print(f"指标文件: {metrics_file}")
        
        return metrics


def main():
    """示例用法"""
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    y_true = np.random.binomial(1, 0.3, n_samples)  # 30%正样本
    y_prob = np.random.beta(2, 5, n_samples)  # 模拟预测概率
    
    # 创建评估器
    evaluator = ModelEvaluator("example_evaluation")
    
    # 生成评估报告
    metrics = evaluator.generate_report(y_true, y_prob, y_prob, 
                                      model_name="Example_Model", 
                                      dataset_name="Example_Data")


if __name__ == "__main__":
    main()
