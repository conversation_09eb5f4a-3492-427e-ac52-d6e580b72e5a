# dMaSIF 现有评估指标分析

## 1. 当前实现的评估指标

### 1.1 主要指标

#### ROC-AUC (Receiver Operating Characteristic - Area Under Curve)
- **位置**: `data_iteration.py` 第383-392行
- **计算方式**: 
```python
roc_auc = roc_auc_score(
    np.rint(numpy(sampled_labels.view(-1))),
    numpy(sampled_preds.view(-1)),
)
```
- **用途**: 评估二分类性能，衡量模型区分正负样本的能力
- **范围**: 0-1，越接近1性能越好

#### 损失函数 (Binary Cross-Entropy Loss)
- **位置**: `data_iteration.py` 第213行
- **计算方式**:
```python
loss = F.binary_cross_entropy_with_logits(preds_concat, labels_concat)
```
- **用途**: 训练过程中的优化目标
- **特点**: 适用于二分类任务

### 1.2 距离相关指标

#### Distance/Positives & Distance/Negatives
- **记录位置**: `main_training.py` 第130-131行
- **用途**: 监控正负样本的距离分布
- **意义**: 帮助理解模型学习的特征空间分布

#### Matching ROC-AUC
- **记录位置**: `main_training.py` 第132行
- **用途**: 专门用于蛋白质匹配任务的ROC-AUC评估
- **适用场景**: 当 `args.search=True` 时使用

### 1.3 表示学习质量指标

#### R_values (Representation Rank)
- **位置**: `model.py` 第468-470行
- **计算方式**:
```python
R_values["input"] = soft_dimension(P1P2["input_features"])
R_values["conv"] = soft_dimension(P1P2["embedding_1"])
```
- **用途**: 监控特征表示的有效维度
- **意义**: 评估模型学习到的表示的复杂度

## 2. 缺失的重要评估指标

### 2.1 准确率 (Accuracy)
- **状态**: ❌ 未实现
- **重要性**: 基础分类指标，直观反映预测正确率
- **计算公式**: `(TP + TN) / (TP + TN + FP + FN)`

### 2.2 混淆矩阵 (Confusion Matrix)
- **状态**: ❌ 未实现  
- **重要性**: 提供详细的分类结果分析
- **包含信息**: True Positives, False Positives, True Negatives, False Negatives

### 2.3 精确率 (Precision)
- **状态**: ❌ 未实现
- **重要性**: 衡量预测为正的样本中实际为正的比例
- **计算公式**: `TP / (TP + FP)`

### 2.4 召回率 (Recall/Sensitivity)
- **状态**: ❌ 未实现
- **重要性**: 衡量实际为正的样本中被正确预测的比例
- **计算公式**: `TP / (TP + FN)`

### 2.5 F1分数 (F1-Score)
- **状态**: ❌ 未实现
- **重要性**: 精确率和召回率的调和平均数
- **计算公式**: `2 * (Precision * Recall) / (Precision + Recall)`

### 2.6 特异性 (Specificity)
- **状态**: ❌ 未实现
- **重要性**: 衡量实际为负的样本中被正确预测的比例
- **计算公式**: `TN / (TN + FP)`

### 2.7 马修斯相关系数 (Matthews Correlation Coefficient, MCC)
- **状态**: ❌ 未实现
- **重要性**: 平衡的分类性能指标，适用于不平衡数据集
- **计算公式**: `(TP*TN - FP*FN) / sqrt((TP+FP)(TP+FN)(TN+FP)(TN+FN))`

### 2.8 PR-AUC (Precision-Recall Area Under Curve)
- **状态**: ❌ 未实现
- **重要性**: 在不平衡数据集上比ROC-AUC更有意义
- **用途**: 评估精确率-召回率曲线下面积

## 3. 当前评估流程

### 3.1 训练时评估
```python
# main_training.py 第115-123行
info = iterate(
    net, dataloader, optimizer, args,
    test=test,
    summary_writer=writer,
    epoch_number=i,
)

# 记录指标到TensorBoard
for key, val in info.items():
    if key in ["Loss", "ROC-AUC", "Distance/Positives", 
               "Distance/Negatives", "Matching ROC-AUC"]:
        writer.add_scalar(f"{key}/{suffix}", np.mean(val), i)
```

### 3.2 推理时评估
```python
# main_inference.py 第78-86行
info = iterate(
    net, test_loader, None, args,
    test=True,
    save_path=save_predictions_path,
    pdb_ids=test_pdb_ids,
)
```

## 4. 评估指标的局限性

### 4.1 当前指标的问题
1. **指标单一**: 主要依赖ROC-AUC，缺乏全面的性能评估
2. **阈值依赖**: 没有明确的分类阈值选择策略
3. **类别不平衡**: 未考虑蛋白质相互作用数据的不平衡特性
4. **统计显著性**: 缺乏置信区间和统计检验

### 4.2 生物学意义
1. **位点预测精度**: 需要更精确的位点级别评估
2. **结构相关性**: 缺乏与实际结构的相关性分析
3. **功能验证**: 需要与已知功能位点的对比验证

## 5. 改进建议

### 5.1 立即需要添加的指标
1. ✅ **准确率**: 基础分类性能
2. ✅ **混淆矩阵**: 详细分类结果
3. ✅ **精确率/召回率**: 平衡性能评估
4. ✅ **F1分数**: 综合性能指标

### 5.2 进阶评估指标
1. **PR-AUC**: 不平衡数据集评估
2. **MCC**: 平衡的相关性指标
3. **Top-K准确率**: 排序性能评估
4. **交叉验证**: 模型稳定性评估

### 5.3 可视化改进
1. **ROC曲线**: 完整的ROC曲线绘制
2. **PR曲线**: 精确率-召回率曲线
3. **混淆矩阵热图**: 直观的分类结果展示
4. **特征重要性**: 模型解释性分析
