#!/usr/bin/env python3
"""
dMaSIF 日志读取工具
用于读取和分析TensorBoard日志文件
"""

import os
import glob
import pandas as pd
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
import argparse

def read_tensorboard_logs(log_dir):
    """读取TensorBoard日志文件"""
    
    # 查找所有事件文件
    event_files = glob.glob(os.path.join(log_dir, "events.out.tfevents.*"))
    
    if not event_files:
        print(f"在 {log_dir} 中没有找到日志文件")
        return None
    
    # 使用最新的事件文件
    latest_file = max(event_files, key=os.path.getctime)
    print(f"读取日志文件: {latest_file}")
    
    # 创建事件累加器
    ea = EventAccumulator(latest_file)
    ea.Reload()
    
    # 获取所有标量标签
    scalar_tags = ea.Tags()['scalars']
    print(f"可用的指标: {scalar_tags}")
    
    # 提取数据
    data = {}
    for tag in scalar_tags:
        scalar_events = ea.Scalars(tag)
        steps = [event.step for event in scalar_events]
        values = [event.value for event in scalar_events]
        times = [event.wall_time for event in scalar_events]
        
        data[tag] = {
            'steps': steps,
            'values': values,
            'times': times
        }
    
    return data

def plot_training_curves(data, save_path=None):
    """绘制训练曲线"""
    
    if not data:
        print("没有数据可绘制")
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('dMaSIF Training Metrics', fontsize=16)
    
    # 损失曲线
    if 'Loss/train' in data:
        axes[0, 0].plot(data['Loss/train']['steps'], data['Loss/train']['values'], 'b-', label='Train Loss')
        if 'Loss/val' in data:
            axes[0, 0].plot(data['Loss/val']['steps'], data['Loss/val']['values'], 'r-', label='Val Loss')
        axes[0, 0].set_title('Loss')
        axes[0, 0].set_xlabel('Step')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
    
    # 准确率曲线
    if 'Accuracy/train' in data:
        axes[0, 1].plot(data['Accuracy/train']['steps'], data['Accuracy/train']['values'], 'b-', label='Train Acc')
        if 'Accuracy/val' in data:
            axes[0, 1].plot(data['Accuracy/val']['steps'], data['Accuracy/val']['values'], 'r-', label='Val Acc')
        axes[0, 1].set_title('Accuracy')
        axes[0, 1].set_xlabel('Step')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    
    # AUC曲线
    if 'AUC/train' in data:
        axes[1, 0].plot(data['AUC/train']['steps'], data['AUC/train']['values'], 'b-', label='Train AUC')
        if 'AUC/val' in data:
            axes[1, 0].plot(data['AUC/val']['steps'], data['AUC/val']['values'], 'r-', label='Val AUC')
        axes[1, 0].set_title('AUC')
        axes[1, 0].set_xlabel('Step')
        axes[1, 0].set_ylabel('AUC')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
    
    # 学习率曲线
    if 'Learning_Rate' in data:
        axes[1, 1].plot(data['Learning_Rate']['steps'], data['Learning_Rate']['values'], 'g-')
        axes[1, 1].set_title('Learning Rate')
        axes[1, 1].set_xlabel('Step')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()

def export_to_csv(data, output_file):
    """导出数据到CSV文件"""
    
    if not data:
        print("没有数据可导出")
        return
    
    # 创建DataFrame
    all_data = []
    for metric, values in data.items():
        for i, (step, value, time) in enumerate(zip(values['steps'], values['values'], values['times'])):
            all_data.append({
                'metric': metric,
                'step': step,
                'value': value,
                'wall_time': time
            })
    
    df = pd.DataFrame(all_data)
    df.to_csv(output_file, index=False)
    print(f"数据已导出到: {output_file}")

def print_summary(data):
    """打印训练摘要"""
    
    if not data:
        print("没有数据可显示")
        return
    
    print("\n" + "="*50)
    print("训练摘要")
    print("="*50)
    
    for metric, values in data.items():
        if values['values']:
            latest_value = values['values'][-1]
            max_value = max(values['values'])
            min_value = min(values['values'])
            
            print(f"{metric}:")
            print(f"  最新值: {latest_value:.4f}")
            print(f"  最大值: {max_value:.4f}")
            print(f"  最小值: {min_value:.4f}")
            print(f"  总步数: {len(values['values'])}")
            print()

def main():
    parser = argparse.ArgumentParser(description='读取dMaSIF训练日志')
    parser.add_argument('--experiment', '-e', type=str, required=True,
                       help='实验名称 (runs目录下的文件夹名)')
    parser.add_argument('--plot', '-p', action='store_true',
                       help='绘制训练曲线')
    parser.add_argument('--export', '-x', type=str,
                       help='导出CSV文件路径')
    parser.add_argument('--summary', '-s', action='store_true',
                       help='显示训练摘要')
    
    args = parser.parse_args()
    
    # 构建日志目录路径
    log_dir = os.path.join('runs', args.experiment)
    
    if not os.path.exists(log_dir):
        print(f"实验目录不存在: {log_dir}")
        print("可用的实验:")
        for exp in os.listdir('runs'):
            if os.path.isdir(os.path.join('runs', exp)):
                print(f"  - {exp}")
        return
    
    # 读取日志数据
    data = read_tensorboard_logs(log_dir)
    
    if data is None:
        return
    
    # 显示摘要
    if args.summary:
        print_summary(data)
    
    # 绘制图表
    if args.plot:
        save_path = f"{args.experiment}_training_curves.png"
        plot_training_curves(data, save_path)
    
    # 导出CSV
    if args.export:
        export_to_csv(data, args.export)

if __name__ == "__main__":
    main()
