# dMaSIF 训练和推理示例

## 🚀 训练示例

### 1. 蛋白质相互作用位点预测 (Site Prediction)

```bash
# 基础位点预测训练
python main_training.py \
    --experiment_name site_basic \
    --site True \
    --single_protein True \
    --random_rotation True \
    --batch_size 64 \
    --n_epochs 50

# 不同半径的位点预测
python main_training.py \
    --experiment_name site_15A \
    --site True \
    --single_protein True \
    --random_rotation True \
    --radius 15.0 \
    --batch_size 64

# 多层网络位点预测
python main_training.py \
    --experiment_name site_3layer \
    --site True \
    --single_protein True \
    --random_rotation True \
    --n_layers 3 \
    --radius 9.0 \
    --batch_size 64
```

### 2. 蛋白质-蛋白质相互作用搜索 (Search/Matching)

```bash
# 基础搜索训练
python main_training.py \
    --experiment_name search_basic \
    --search True \
    --random_rotation True \
    --batch_size 64 \
    --radius 12.0

# 多层搜索网络
python main_training.py \
    --experiment_name search_3layer \
    --search True \
    --random_rotation True \
    --n_layers 3 \
    --radius 12.0 \
    --batch_size 64
```

### 3. 不同网络架构对比

```bash
# 使用DGCNN
python main_training.py \
    --experiment_name dgcnn_site \
    --embedding_layer DGCNN \
    --site True \
    --single_protein True \
    --batch_size 64

# 使用PointNet++
python main_training.py \
    --experiment_name pointnet_site \
    --embedding_layer PointNet++ \
    --site True \
    --single_protein True \
    --batch_size 64
```

### 4. 消融研究

```bash
# 不使用化学信息
python main_training.py \
    --experiment_name no_chem \
    --site True \
    --single_protein True \
    --no_chem True

# 不使用几何信息
python main_training.py \
    --experiment_name no_geom \
    --site True \
    --single_protein True \
    --no_geom True
```

## 🔍 推理示例

### 1. 使用训练好的模型进行推理

```bash
# 在测试集上推理
python main_inference.py \
    --experiment_name site_basic \
    --site True \
    --single_protein True

# 在特定PDB结构上推理
python main_inference.py \
    --experiment_name site_basic \
    --site True \
    --single_protein True \
    --single_pdb 1A2K_A

# 在PDB列表上推理
python main_inference.py \
    --experiment_name site_basic \
    --site True \
    --single_protein True \
    --pdb_list lists/testing.txt
```

### 2. 搜索任务推理

```bash
# 蛋白质匹配推理
python main_inference.py \
    --experiment_name search_basic \
    --search True \
    --random_rotation True
```

## 📊 参数调优建议

### 位点预测任务
- **半径**: 5.0-15.0Å (推荐9.0-12.0Å)
- **层数**: 1-3层 (更多层可能过拟合)
- **批大小**: 32-64 (根据GPU内存调整)
- **数据增强**: 建议开启random_rotation

### 搜索任务
- **半径**: 12.0-15.0Å (需要更大感受野)
- **层数**: 1-3层
- **批大小**: 16-32 (搜索任务内存需求更大)

### 性能优化
- **GPU内存不足**: 减小batch_size或radius
- **训练太慢**: 减小sup_sampling或resolution
- **精度不够**: 增加n_layers或in_channels

## 🛠️ 常见问题解决

### 1. CUDA内存不足
```bash
# 减小批大小
--batch_size 16

# 减小卷积半径
--radius 6.0

# 减小分辨率
--resolution 1.5
```

### 2. 训练不收敛
```bash
# 增加数据增强
--random_rotation True

# 调整学习率相关参数
--dropout 0.1

# 增加网络容量
--in_channels 32
--n_layers 2
```

### 3. 推理速度慢
```bash
# 减小表面采样
--sup_sampling 10

# 使用预计算表面
--use_mesh True
```

## 📁 输出文件说明

- `runs/实验名称/`: TensorBoard日志
- `models/实验名称`: 训练好的模型权重
- `preds/实验名称/`: 推理结果
- `surface_data/processed/`: 预处理的表面数据
