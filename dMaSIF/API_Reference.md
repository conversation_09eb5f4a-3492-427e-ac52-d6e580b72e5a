# dMaSIF API 参考文档

## 核心类和函数

### 1. dMaSIF 主模型类

```python
class dMaSIF(nn.Module):
    """dMaSIF主模型类，实现端到端蛋白质表面学习"""
    
    def __init__(self, args):
        """
        初始化dMaSIF模型
        
        Args:
            args: 参数对象，包含所有模型配置
                - curvature_scales: list, 曲率计算尺度
                - atom_dims: int, 原子特征维度
                - in_channels: int, 输入通道数
                - emb_dims: int, 嵌入维度
                - n_layers: int, 卷积层数
                - radius: float, 卷积半径
                - embedding_layer: str, 嵌入层类型
                - site: bool, 是否进行位点预测
                - search: bool, 是否进行配对搜索
        """
    
    def features(self, P, i=1):
        """
        从蛋白质表面估计几何和化学特征
        
        Args:
            P: dict, 蛋白质数据字典
                - atoms: Tensor [N_atoms, 3], 原子坐标
                - atomtypes: Tensor [N_atoms, 6], 原子类型
                - batch_atoms: Tensor [N_atoms], 原子批次索引
                - xyz: Tensor [N_points, 3], 表面点坐标（可选）
                - normals: Tensor [N_points, 3], 法向量（可选）
                - triangles: Tensor [N_triangles, 3], 三角网格（可选）
                - batch: Tensor [N_points], 表面点批次索引（可选）
            i: int, 蛋白质索引
            
        Returns:
            Tensor [N_points, feature_dim]: 拼接的几何和化学特征
                - feature_dim = len(curvature_scales) * 2 + atom_dims
        """
    
    def embed(self, P):
        """
        将蛋白质所有点嵌入到高维向量空间
        
        Args:
            P: dict, 蛋白质数据字典
            
        Returns:
            tuple: (conv_time, memory_usage)
                - conv_time: float, 卷积计算时间（秒）
                - memory_usage: int, 内存使用量（字节）
                
        Side Effects:
            在P中添加以下键：
            - input_features: Tensor [N_points, feature_dim], 输入特征
            - embedding_1: Tensor [N_points, emb_dims], 第一个嵌入
            - embedding_2: Tensor [N_points, emb_dims], 第二个嵌入（如果search=True）
        """
    
    def forward(self, P1, P2=None):
        """
        模型前向传播
        
        Args:
            P1: dict, 第一个蛋白质数据
            P2: dict, 第二个蛋白质数据（可选，用于配对任务）
            
        Returns:
            dict: 包含以下键的结果字典
                - P1: dict, 处理后的第一个蛋白质数据
                - P2: dict, 处理后的第二个蛋白质数据（如果提供）
                - R_values: dict, 表示维度信息
                - conv_time: float, 卷积时间
                - memory_usage: int, 内存使用量
        """
```

### 2. 几何处理函数

```python
def atoms_to_points_normals(atoms, batch_atoms, atomtypes, 
                           resolution=1.0, sup_sampling=20, distance=1.05):
    """
    从原子坐标生成表面点云和法向量
    
    Args:
        atoms: Tensor [N_atoms, 3], 原子坐标
        batch_atoms: Tensor [N_atoms], 原子批次索引
        atomtypes: Tensor [N_atoms, 6], 原子类型one-hot编码
        resolution: float, 点云分辨率（埃）
        sup_sampling: int, 原子周围超采样比例
        distance: float, 表面生成距离参数
        
    Returns:
        tuple: (xyz, normals, batch)
            - xyz: Tensor [N_points, 3], 表面点坐标
            - normals: Tensor [N_points, 3], 表面法向量
            - batch: Tensor [N_points], 表面点批次索引
    """

def curvatures(points, triangles=None, normals=None, 
               scales=[1.0, 2.0, 3.0, 5.0, 10.0], batch=None):
    """
    计算多尺度曲率特征
    
    Args:
        points: Tensor [N_points, 3], 表面点坐标
        triangles: Tensor [N_triangles, 3], 三角网格连接（可选）
        normals: Tensor [N_points, 3], 法向量（可选）
        scales: list, 计算曲率的尺度列表
        batch: Tensor [N_points], 批次索引（可选）
        
    Returns:
        Tensor [N_points, 2*len(scales)]: 多尺度平均曲率和高斯曲率
            - 前len(scales)列为平均曲率
            - 后len(scales)列为高斯曲率
    """

def subsample(x, batch=None, scale=1.0):
    """
    使用网格聚类对点云进行子采样
    
    Args:
        x: Tensor [N, 3], 输入点云
        batch: Tensor [N], 批次索引（可选）
        scale: float, 网格单元大小
        
    Returns:
        Tensor [M, 3]: 子采样后的点云，M <= N
    """

def save_vtk(fname, xyz, triangles=None, values=None, vectors=None):
    """
    保存点云或三角网格为VTK格式
    
    Args:
        fname: str, 输出文件名
        xyz: Tensor [N, 3], 点坐标或顶点坐标
        triangles: Tensor [T, 3], 三角网格连接（可选）
        values: Tensor [N, D], 点值（可选）
        vectors: Tensor [N, 3], 点向量（可选）
    """
```

### 3. 原子特征提取

```python
class AtomNet_MP(nn.Module):
    """基于消息传递的原子特征提取网络"""
    
    def __init__(self, args):
        """
        初始化原子网络
        
        Args:
            args: 参数对象
                - atom_dims: int, 原子特征维度
        """
    
    def forward(self, xyz, atom_xyz, atomtypes, batch, atom_batch):
        """
        提取原子特征
        
        Args:
            xyz: Tensor [N_points, 3], 表面点坐标
            atom_xyz: Tensor [N_atoms, 3], 原子坐标
            atomtypes: Tensor [N_atoms, atom_dims], 原子类型特征
            batch: Tensor [N_points], 表面点批次索引
            atom_batch: Tensor [N_atoms], 原子批次索引
            
        Returns:
            Tensor [N_points, atom_dims]: 表面点的原子特征
        """

def get_atom_features(x, y, x_batch, y_batch, y_atomtype, k=16):
    """
    获取表面点的原子特征
    
    Args:
        x: Tensor [N_points, 3], 表面点坐标
        y: Tensor [N_atoms, 3], 原子坐标
        x_batch: Tensor [N_points], 表面点批次索引
        y_batch: Tensor [N_atoms], 原子批次索引
        y_atomtype: Tensor [N_atoms, atom_dims], 原子类型特征
        k: int, k近邻数量
        
    Returns:
        Tensor [N_points, k, atom_dims+1]: 每个表面点的k个最近原子特征
    """
```

### 4. 数据加载和预处理

```python
class ProteinPairsSurfaces(InMemoryDataset):
    """蛋白质配对表面数据集"""
    
    def __init__(self, root, train=True, ppi=False, transform=None):
        """
        初始化数据集
        
        Args:
            root: str, 数据根目录
            train: bool, 是否为训练集
            ppi: bool, 是否为蛋白质-蛋白质相互作用数据
            transform: callable, 数据变换函数
        """

def load_protein_pair(pdb_id, data_dir, single_pdb=False):
    """
    加载单个蛋白质配对数据
    
    Args:
        pdb_id: str, PDB标识符
        data_dir: Path, 数据目录
        single_pdb: bool, 是否为单个PDB文件
        
    Returns:
        Data: PyTorch Geometric数据对象
    """

class RandomRotationPairAtoms(object):
    """随机旋转蛋白质配对的数据变换"""
    
    def __call__(self, data):
        """
        应用随机旋转
        
        Args:
            data: Data, 输入数据对象
            
        Returns:
            Data: 旋转后的数据对象
        """

class CenterPairAtoms(object):
    """中心化蛋白质配对的数据变换"""
    
    def __call__(self, data):
        """
        中心化坐标
        
        Args:
            data: Data, 输入数据对象
            
        Returns:
            Data: 中心化后的数据对象
        """
```

### 5. 训练和推理函数

```python
def iterate(net, dataloader, optimizer, args, test=False, save_path=None, pdb_ids=None):
    """
    执行一轮训练或测试迭代
    
    Args:
        net: nn.Module, 神经网络模型
        dataloader: DataLoader, 数据加载器
        optimizer: Optimizer, 优化器（训练时需要）
        args: 参数对象
        test: bool, 是否为测试模式
        save_path: Path, 保存预测结果的路径（可选）
        pdb_ids: list, PDB标识符列表（可选）
        
    Returns:
        dict: 包含损失、准确率等指标的字典
            - loss: float, 平均损失
            - accuracy: float, 准确率
            - roc_auc: float, ROC AUC分数
            - conv_time: list, 卷积时间列表
            - memory_usage: list, 内存使用量列表
    """

def iterate_surface_precompute(dataloader, net, args):
    """
    预计算表面数据
    
    Args:
        dataloader: DataLoader, 数据加载器
        net: nn.Module, 神经网络模型
        args: 参数对象
        
    Returns:
        list: 预处理后的数据列表
    """
```

### 6. 辅助函数

```python
def soft_dimension(X, threshold=0.99):
    """
    计算张量的软维度（有效维度）
    
    Args:
        X: Tensor [N, D], 输入张量
        threshold: float, 累积方差阈值
        
    Returns:
        float: 软维度值
    """

def diagonal_ranges(batch_x, batch_y):
    """
    为KeOps LazyTensor计算对角线范围
    
    Args:
        batch_x: Tensor [N], 第一组批次索引
        batch_y: Tensor [M], 第二组批次索引
        
    Returns:
        Tensor: 对角线范围张量
    """

def knn_atoms(x, y, x_batch, y_batch, k):
    """
    计算原子间的k近邻
    
    Args:
        x: Tensor [N, 3], 查询点
        y: Tensor [M, 3], 参考点
        x_batch: Tensor [N], 查询点批次索引
        y_batch: Tensor [M], 参考点批次索引
        k: int, 近邻数量
        
    Returns:
        tuple: (idx, dists)
            - idx: Tensor [N, k], 近邻索引
            - dists: Tensor [N, k], 近邻距离
    """
```

## 参数配置

### 命令行参数完整列表

```python
# 主要参数
--experiment_name: str          # 实验名称（必需）
--use_mesh: bool = False        # 是否使用预计算表面
--embedding_layer: str = "dMaSIF"  # 嵌入层类型
--profile: bool = False         # 是否启用性能分析

# 几何参数
--curvature_scales: list = [1.0, 2.0, 3.0, 5.0, 10.0]  # 曲率计算尺度
--resolution: float = 1.0       # 点云分辨率
--distance: float = 1.05        # 表面生成距离参数
--variance: float = 0.1         # 表面生成方差参数
--sup_sampling: int = 20        # 原子周围超采样比例

# 网络超参数
--atom_dims: int = 6            # 原子类型维度
--emb_dims: int = 8             # 嵌入维度
--in_channels: int = 16         # 输入通道数
--orientation_units: int = 16   # 方向分数MLP隐藏单元数
--post_units: int = 8           # 后处理MLP隐藏单元数
--n_layers: int = 1             # 卷积层数
--radius: float = 9.0           # 卷积半径
--k: int = 40                   # DGCNN和PointNet++的近邻数
--dropout: float = 0.0          # Dropout比例

# 训练参数
--n_epochs: int = 50            # 训练轮数
--batch_size: int = 1           # 批次大小
--device: str = "cuda:0"        # 设备
--restart_training: str = ""    # 重启训练的模型路径
--validation_fraction: float = 0.1  # 验证集比例
--seed: int = 42                # 随机种子
--random_rotation: bool = False # 是否随机旋转
--single_protein: bool = False  # 是否使用单个蛋白质

# 任务参数
--site: bool = False            # 是否进行位点预测
--search: bool = False          # 是否进行配对搜索
--no_chem: bool = False         # 是否不使用化学信息
--no_geom: bool = False         # 是否不使用几何信息

# 推理参数
--single_pdb: str = ""          # 单个PDB推理
--pdb_list: str = ""            # PDB列表文件
```

## 错误处理

### 常见异常类型

```python
# CUDA内存不足
RuntimeError: CUDA out of memory

# 数据格式错误
ValueError: Invalid atom type

# 文件不存在
FileNotFoundError: PDB file not found

# 维度不匹配
RuntimeError: size mismatch
```

### 调试建议

1. **启用详细日志**: 使用 `--profile True`
2. **检查数据格式**: 验证PDB文件和原子类型
3. **减少内存使用**: 降低batch_size和radius
4. **使用CPU模式**: 设置 `--device cpu` 进行调试
