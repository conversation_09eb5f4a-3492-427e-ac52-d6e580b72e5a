# dMaSIF 数据集结构详细说明

## 1. 数据集概览

dMaSIF使用基于MaSIF基准数据集的蛋白质表面数据，专门用于蛋白质-蛋白质相互作用预测任务。

### 1.1 数据集来源
- **基础数据集**: MaSIF (Molecular Surface Interaction Fingerprinting)
- **数据类型**: 蛋白质表面网格数据 + 原子坐标数据
- **任务类型**: 
  - 相互作用位点预测 (Site Prediction)
  - 蛋白质匹配搜索 (Protein Matching Search)

## 2. 目录结构

### 2.1 主要目录布局
```
dMaSIF/
├── surface_data/                    # 主数据目录
│   ├── raw/                        # 原始数据
│   │   ├── 01-benchmark_pdbs/      # PDB结构文件
│   │   ├── 01-benchmark_surfaces/  # PLY表面文件
│   │   └── 01-benchmark_surfaces_npy/ # 转换后的numpy文件
│   └── processed/                  # 处理后的数据
│       ├── training_pairs_data.pt  # 训练集数据
│       ├── testing_pairs_data.pt   # 测试集数据
│       ├── training_pairs_data_ids.npy # 训练集ID列表
│       └── testing_pairs_data_ids.npy  # 测试集ID列表
├── lists/                          # 数据集划分列表
│   ├── training.txt               # 训练集蛋白质列表
│   ├── testing.txt                # 测试集蛋白质列表
│   ├── training_ppi.txt           # 训练集蛋白质对列表
│   └── testing_ppi.txt            # 测试集蛋白质对列表
└── data_preprocessing/             # 数据预处理脚本
    ├── convert_pdb2npy.py         # PDB转numpy
    ├── convert_ply2npy.py         # PLY转numpy
    └── download_pdb.py            # PDB下载脚本
```

## 3. 数据格式详解

### 3.1 原始数据格式

#### PDB文件 (Protein Data Bank)
- **位置**: `surface_data/raw/01-benchmark_pdbs/`
- **格式**: `.pdb`
- **内容**: 蛋白质三维结构坐标
- **命名规则**: `{PDB_ID}_{CHAIN}.pdb` (例如: `1A2K_A.pdb`)

#### PLY文件 (Polygon File Format)
- **位置**: `surface_data/raw/01-benchmark_surfaces/`
- **格式**: `.ply`
- **内容**: 蛋白质表面网格数据
- **包含信息**:
  - 表面顶点坐标
  - 三角网格连接信息
  - 表面法向量
  - 化学特征

### 3.2 预处理后的数据格式

#### Numpy数组文件
每个蛋白质链对应多个numpy文件：

```python
# 原子相关数据
{PDB_ID}_{CHAIN}_atomxyz.npy     # 原子坐标 [N_atoms, 3]
{PDB_ID}_{CHAIN}_atomtypes.npy   # 原子类型 [N_atoms, 6] (one-hot编码)

# 表面相关数据  
{PDB_ID}_{CHAIN}_xyz.npy         # 表面点坐标 [N_points, 3]
{PDB_ID}_{CHAIN}_triangles.npy   # 三角网格 [N_triangles, 3]
{PDB_ID}_{CHAIN}_normals.npy     # 表面法向量 [N_points, 3]
{PDB_ID}_{CHAIN}_features.npy    # 化学特征 [N_points, N_features]
{PDB_ID}_{CHAIN}_iface_labels.npy # 相互作用标签 [N_points, 1]
```

#### 原子类型编码
```python
ele2num = {"C": 0, "H": 1, "O": 2, "N": 3, "S": 4, "SE": 5}
# 转换为6维one-hot向量
# 例如: 碳原子 -> [1, 0, 0, 0, 0, 0]
```

### 3.3 PyTorch数据结构

#### PairData类
```python
class PairData(Data):
    def __init__(self):
        # 蛋白质1的数据
        self.xyz_p1              # [N1, 3] 表面点坐标
        self.face_p1             # [N1_tri, 3] 三角网格
        self.chemical_features_p1 # [N1, F] 化学特征
        self.y_p1                # [N1, 1] 相互作用标签
        self.normals_p1          # [N1, 3] 表面法向量
        self.atom_coords_p1      # [N1_atoms, 3] 原子坐标
        self.atom_types_p1       # [N1_atoms, 6] 原子类型
        
        # 蛋白质2的数据 (结构相同)
        self.xyz_p2, self.face_p2, ...
        
        # 数据增强相关
        self.atom_center1        # 原子中心坐标
        self.rand_rot1           # 随机旋转矩阵
```

## 4. 数据集划分

### 4.1 训练/测试划分

#### 单蛋白质模式 (Site Prediction)
- **训练集**: `lists/training.txt`
- **测试集**: `lists/testing.txt`
- **样本数量**: 
  - 训练集: ~1000个蛋白质链
  - 测试集: ~300个蛋白质链

#### 蛋白质对模式 (Protein Matching)
- **训练集**: `lists/training_ppi.txt`
- **测试集**: `lists/testing_ppi.txt`
- **样本数量**:
  - 训练集: ~800个蛋白质对
  - 测试集: ~200个蛋白质对

### 4.2 数据加载流程

```python
# 1. 创建数据集对象
train_dataset = ProteinPairsSurfaces(
    "surface_data", 
    ppi=args.search,      # True: 蛋白质对模式, False: 单蛋白质模式
    train=True,           # True: 训练集, False: 测试集
    transform=transformations
)

# 2. 数据过滤
train_dataset = [data for data in train_dataset if iface_valid_filter(data)]

# 3. 创建数据加载器
train_loader = DataLoader(
    train_dataset, 
    batch_size=1, 
    follow_batch=["xyz_p1", "xyz_p2", "atom_coords_p1", "atom_coords_p2"],
    shuffle=True
)
```

## 5. 数据预处理

### 5.1 数据变换 (Transformations)

#### NormalizeChemFeatures
- **功能**: 标准化化学特征
- **作用**: 确保不同特征维度的数值范围一致

#### CenterPairAtoms  
- **功能**: 将蛋白质对的原子坐标居中
- **作用**: 消除平移变换的影响

#### RandomRotationPairAtoms
- **功能**: 随机旋转蛋白质对
- **作用**: 数据增强，提高模型的旋转不变性

### 5.2 表面预计算

```python
# 表面特征预计算
train_dataset = iterate_surface_precompute(train_loader, net, args)
```

- **目的**: 预先计算表面的几何特征
- **包含**: 曲率、法向量、化学特征等
- **优势**: 加速训练过程

## 6. 标签生成

### 6.1 相互作用标签

#### 位点预测标签
- **来源**: PLY文件中的预标注标签
- **含义**: 1表示相互作用位点，0表示非相互作用位点
- **生成方式**: 基于已知的蛋白质复合物结构

#### 匹配标签 (搜索模式)
```python
def generate_matchinglabels(args, P1, P2):
    # 计算两个蛋白质表面点之间的距离
    xyz_dists = ((xyz1_i - xyz2_j) ** 2).sum(-1).sqrt()
    xyz_dists = (1.0 - xyz_dists).step()
    
    # 生成相互作用标签
    p1_iface_labels = (xyz_dists.sum(1) > 1.0).float()
    p2_iface_labels = (xyz_dists.sum(0) > 1.0).float()
```

## 7. 数据质量控制

### 7.1 数据过滤

#### iface_valid_filter
- **功能**: 过滤无效的相互作用数据
- **标准**: 确保蛋白质对具有有效的相互作用界面

### 7.2 数据验证
- **完整性检查**: 确保所有必需的文件都存在
- **格式验证**: 验证numpy数组的形状和数据类型
- **标签一致性**: 检查标签与结构数据的一致性

## 8. 数据集特点

### 8.1 优势
1. **高质量标注**: 基于实验确定的蛋白质复合物结构
2. **多尺度特征**: 包含原子级和表面级特征
3. **标准化基准**: 使用广泛认可的MaSIF基准数据集
4. **几何丰富**: 包含详细的表面几何信息

### 8.2 挑战
1. **数据不平衡**: 相互作用位点通常是少数类
2. **计算复杂**: 表面网格数据计算量大
3. **内存需求**: 大型蛋白质的表面数据占用内存较多
4. **标注噪声**: 实验结构可能存在不确定性
