{"SHS27k": {"random": {"dataset": "SHS27k", "split_mode": "random", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 0, "data_split_mode": 0}, "dfs": {"dataset": "SHS27k", "split_mode": "dfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 512, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 0, "data_split_mode": 2}, "bfs": {"dataset": "SHS27k", "split_mode": "bfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.2, "sce_scale": 1.5, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 0, "data_split_mode": 1}}, "SHS148k": {"random": {"dataset": "SHS148k", "split_mode": "random", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.1, "sce_scale": 1, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 1, "data_split_mode": 0}, "dfs": {"dataset": "SHS148k", "split_mode": "dfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.15, "sce_scale": 2, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 1, "data_split_mode": 2}, "bfs": {"dataset": "SHS148k", "split_mode": "bfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 512, "mask_ratio": 0.2, "sce_scale": 2, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 1, "data_split_mode": 1}}, "STRING": {"random": {"dataset": "STRING", "split_mode": "random", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.1, "sce_scale": 2, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 2, "data_split_mode": 0}, "dfs": {"dataset": "STRING", "split_mode": "dfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 256, "mask_ratio": 0.2, "sce_scale": 1.5, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 2, "data_split_mode": 2}, "bfs": {"dataset": "STRING", "split_mode": "bfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 256, "mask_ratio": 0.1, "sce_scale": 1.5, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 2, "data_split_mode": 1}}, "Arabidopsis": {"random": {"dataset": "Arabidopsis", "split_mode": "random", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 3, "data_split_mode": 0}, "dfs": {"dataset": "Arabidopsis", "split_mode": "dfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 512, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 3, "data_split_mode": 2}, "bfs": {"dataset": "Arabidopsis", "split_mode": "bfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.2, "sce_scale": 1.5, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 3, "data_split_mode": 1}}, "rice": {"random": {"dataset": "rice", "split_mode": "random", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 4, "data_split_mode": 0}, "dfs": {"dataset": "rice", "split_mode": "dfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 128, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 512, "mask_ratio": 0.15, "sce_scale": 1.5, "mask_loss": 1, "seed": 0, "log_num": 10, "data_mode": 4, "data_split_mode": 2}, "bfs": {"dataset": "rice", "split_mode": "bfs", "input_dim": 7, "output_dim": 8, "ppi_hidden_dim": 1024, "prot_hidden_dim": 256, "ppi_num_layers": 2, "prot_num_layers": 4, "learning_rate": 0.001, "weight_decay": 0.0001, "max_epoch": 500, "batch_size": 10000, "dropout_ratio": 0.0, "pre_epoch": 50, "commitment_cost": 0.25, "num_embeddings": 1024, "mask_ratio": 0.2, "sce_scale": 1.5, "mask_loss": 0.5, "seed": 0, "log_num": 10, "data_mode": 4, "data_split_mode": 1}}}