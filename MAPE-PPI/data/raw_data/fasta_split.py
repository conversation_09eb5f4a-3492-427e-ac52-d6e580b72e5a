﻿def load_pairs(file_path):
    pairs = []
    with open(file_path, 'r') as rf:
        for pair in rf:
            pept, prot = pair.split()
            pairs.append([pept.strip(), prot.strip()])
    return pairs

def load_sequences(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        name = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                name = line[1:]
                # print("extract")
                # print(line)
                # print(name)
                seq_dict[name] = ""
            else:
                seq = line.upper()
                seq_dict[name] = seq
    return seq_dict
def load_sequences2(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                item = line.split(" ")
                name = item[0][1:-2] 
                seq = ''
                print(name)
                seq_dict[name] = ""
            else:
                seq = line.upper().strip(" ").strip('*')
                if name in seq_dict:
                    seq_dict[name] += seq
                else:
                    seq_dict[name] = seq
    return seq_dict

# pep_fasta_file_path = "./rice/grind_pep_sequence.fasta.txt"
# pep_protein_file_path = "./rice/grind_protein_sequence.fasta"
# pep_fasta_file_path = "./Arabidopsis/pep_sequence.fasta"
# pep_protein_file_path = "./Arabidopsis/rlk_sequence.fasta"
pep_fasta_file_path = "./Arabidopsis2/peptide_sequences.fasta"
pep_protein_file_path = "./Arabidopsis2/protein_sequences.fasta"
seq_dict = load_sequences2(pep_fasta_file_path)
protein_dict = load_sequences2(pep_protein_file_path)
merge_dict = {}
for key,value in seq_dict.items():
    merge_dict[key] = value
for key,value in protein_dict.items():
    merge_dict[key] = value

for key,value in merge_dict.items():
    print(key,value)
    with open("./Arabidopsis_fasta/"+key+".fasta","w+") as f:
        f.write(">"+key+"\n")
        f.write(value+"\n")