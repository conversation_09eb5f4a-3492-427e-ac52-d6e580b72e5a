﻿def load_pairs(file_path):
    pairs = []
    with open(file_path, 'r') as rf:
        for pair in rf:
            pept, prot = pair.split()
            pairs.append([pept.strip(), prot.strip()])
    return pairs

def load_sequences(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        name = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                name = line[1:]
                # print("extract")
                # print(line)
                # print(name)
            else:
                seq = line.upper()
                seq_dict[name] = seq
    return seq_dict
def load_sequences2(file_path):
    seq_dict = {}
    with open(file_path, 'r') as rf:
        seq = ''
        for line in rf:
            line = line.strip()
            if line[0] == '>':
                item = line.split(" ")
                name = item[0][1:-2] 
                seq = ''
                print(name)
                seq_dict[name] = ""
            else:
                seq = line.upper().strip(" ").strip('*')
                if name in seq_dict:
                    seq_dict[name] += seq
                else:
                    seq_dict[name] = seq
    return seq_dict

dir = './Arabidopsis/'
# dir = './rice/'
# file_name = "rice"
file_name = "Arabidopsis"
# pep_file_name = "grind_pep_sequence.fasta.txt"
pep_file_name = "pep_sequence.fasta"
rlk_file_name = "rlk_sequence.fasta"
# rlk_file_name = "grind_protein_sequence.fasta"
positive_pairs_name  = "positive.txt"
# positive_pairs_name  = "grind_positive.txt"
# negative_pairs_name = "grind_negative.txt"
negative_pairs_name = "negative.txt"
real_output_name = f"protein.{file_name}.sequences.dictionary.tsv"
real_output_action_name = f"protein.actions.{file_name}.txt"
# pep_file_name = load_sequences2(dir+pep_file_name)
# rlk_file_name = load_sequences2(dir+rlk_file_name)
pep_file_name = load_sequences(dir+pep_file_name)
rlk_file_name = load_sequences(dir+rlk_file_name)
all_file_pdb = []
map_dict = []
for key,value in pep_file_name.items():
    # print(key,value)
    all_file_pdb.append(key)
    map_dict.append([key,value])
for key,value in rlk_file_name.items():
    # print(key)
    all_file_pdb.append(key)
    map_dict.append([key,value])
all_file_pdb = list(set(all_file_pdb))
# print(all_file_pdb)


import csv

# 准备数据
data = [
    {"Name": "Alice", "Age": 30, "City": "New York"},
    {"Name": "Bob", "Age": 25, "City": "Los Angeles"},
    {"Name": "Charlie", "Age": 35, "City": "Chicago"}
]

# 写入 TSV 文件
with open(real_output_name, "w", newline="", encoding="utf-8") as file:
    fieldnames = ["key", "value"]  # 定义表头
    writer = csv.writer(file,  delimiter="\t")
    writer.writerows(map_dict)  # 写入数据

print("TSV 文件已生成：output.tsv")




# from get_af2_pdb import get_alphafold_pdb
# count = 0
# for name in all_file_pdb:
#     count = count+1
#     print(f"{count}/{len(all_file_pdb)}")
#     get_alphafold_pdb(name)

positive_pairs = load_pairs(dir+positive_pairs_name)
negative_pairs = load_pairs(dir+negative_pairs_name)
for pairs in positive_pairs:
    print(pairs[0],pairs[1])
import random
# real_output_action_name = "output.tsv"
with open(real_output_action_name, "w", newline="", encoding="utf-8") as file:
    fieldnames = ["item_id_a", "item_id_b","mode","action","is_directional","a_is_acting","score"]  # 定义表头
    writer = csv.writer(file,  delimiter="\t")
    writer.writerow(fieldnames)  # 写入表头
    t_array = []
    for pairs in positive_pairs:
        t_array.append((pairs[0],pairs[1],"binding",'','f',"t",900))
    for pairs in negative_pairs:
        t_array.append((pairs[0],pairs[1],"inactivate",'','f',"t",0))
    random.shuffle(t_array)
    writer.writerows(t_array)
    # for pairs in positive_pairs:
    #     writer.writerow((pairs[0],pairs[1],"binding",'','f',"t",900))
    # for pairs in positive_pairs:
    #     writer.writerow((pairs[0],pairs[1],"binding",'','f',"t",0))



