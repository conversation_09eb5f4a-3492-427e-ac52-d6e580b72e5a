﻿import os

# Function to read data and collect unique UniProt IDs
def read_data_and_ids(filename):
    data = []
    unique_ids = set()
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) < 4:
                continue
            item_id_a, item_id_b = parts[0], parts[1]
            data.append((item_id_a, item_id_b))
            unique_ids.update([item_id_a, item_id_b])
    return data, unique_ids

# Read original data and collect IDs
data, unique_ids = read_data_and_ids("./protein.actions.rice.txt")

# Check PDB existence
def check_pdb_existence(unique_ids):
    existence = {}
    for uid in unique_ids:
        af2db_path = os.path.join("./STRING_AF2DB", f"{uid}.pdb")
        string_path = os.path.join("DATABASE", "STRING", f"{uid}.pdb")
        existence[uid] = os.path.exists(af2db_path) or os.path.exists(string_path)
    return existence

# First verification: check which pairs have missing PDB files
print("First verification: Missing PDB files in interactions\n")
print("Item ID A\tItem ID B\tExistence A\tExistence B")
existence = check_pdb_existence(unique_ids)
for a, b in data:
    a_exists = "Exists" if existence[a] else ""
    b_exists = "Exists" if existence[b] else ""
    if not existence[a] or not existence[b]:
        print(f"{a}\t{b}\t{a_exists}\t{b_exists}")

# Reverse verification: check if PDB files in AF2DB directory are referenced
print("\n\nReverse verification: Check orphan PDB files in AF2DB\n")

# Get all PDB files in STRING_AF2DB directory
af2db_files = set()
for filename in os.listdir("./STRING_AF2DB"):
    if filename.endswith(".pdb"):
        # Extract UniProt ID without .pdb extension
        uid = filename[:-4]
        af2db_files.add(uid)

# Check if these IDs exist in the original data
orphan_files = []
for uid in af2db_files:
    found = False
    # Check both directions in data to see if this UID appears
    for a, b in data:
        if uid == a or uid == b:
            found = True
            break
    if not found:
        orphan_files.append(uid)

# Print results for reverse verification
print(f"Total PDB files in AF2DB: {len(af2db_files)}")
print(f"Orphan PDB files not referenced in interactions: {len(orphan_files)}")
for orphan in orphan_files:
    print(orphan)
